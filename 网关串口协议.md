蓝牙MESH网关（初级版）串口协议V1.0
                    编号：BCM/QM10-202112
                    版本： V1.0
                    日期：2021-8-10
www.beancomm.com

# Home Assistant 集成实现说明

本文档描述了<PERSON><PERSON><PERSON>sh Gateway在Home Assistant中的集成实现。

## 实现状态

✅ **已实现功能**：
- 设备发现和管理
- 开关控制（单路/多路）
- 灯光控制（开关/亮度/色温）
- 状态同步
- 实时事件处理

✅ **支持的设备类型**：
- 零火开关 (type=1)
- 单火开关 (type=2)
- 智能插座 (type=3)
- 智能灯 (type=4)
- 五色调光灯 (type=24)
- 透传模块 (type=20, 74)

## 协议实现细节

### 设备控制协议
- **开关控制**: `53 30 04 ADDR MSG_TYPE VALUE`
  - MSG_TYPE = 0x02 (开关控制)
  - VALUE: 0x01=关闭, 0x02=开启
  - 多路开关: 每2位表示一路，bit0-1=第1路，bit2-3=第2路...

- **调光控制**: `53 30 04 ADDR 03 BRIGHTNESS`
  - MSG_TYPE = 0x03 (亮度控制)
  - BRIGHTNESS: 0-100 (百分比)

- **色温控制**: `53 30 04 ADDR 04 COLOR_TEMP`
  - MSG_TYPE = 0x04 (色温控制)
  - COLOR_TEMP: 0-100 (百分比)

### 状态事件处理
- **状态事件**: `53 80 06 XX ADDR MSG_TYPE VALUE...`
  - 自动解析多个msg_type/value对
  - 实时更新Home Assistant实体状态
  - 支持设备在线状态检测

### 测试验证
使用 `test_symi_protocol.py` 脚本可以独立测试协议功能：
```bash
python test_symi_protocol.py
```

## 故障排除

### 常见问题
1. **设备无法控制**: 检查网络地址和设备类型是否正确
2. **状态不同步**: 确保设备事件处理正常工作
3. **调光不可用**: 验证设备类型为智能灯或五色调光灯

### 调试日志
启用DEBUG级别日志查看详细协议交互：
```yaml
logger:
  default: info
  logs:
    custom_components.symi_mesh_gateway: debug
```
1 概述
翼数是蓝牙MESH智能设备全套解决方案服务器，我们独立开发了一套蓝牙MESH主从机系统，整个系统是在SIG MESH框架上完成设计开发，可以给整机客户提供全套解决方案，并且客户可以开发自己的网关产品以及云端服务。
翼数提供的服务包括MESH主机模块，MESH丛机模块，以及MESH网关整机。可以满足各种客户的需求。
本文主要介绍如何使用MESH主机来实现整个智能设备的控制，场景等等应用。
2 MESH产品介绍
2.1 设备分类
我们提供蓝牙MESH的主机和丛机。 
主机分为： 串口主机模块，usb接口调试主机，蓝牙转wifi（带以太网）网关。
丛机分为： 多品类设备模块，可以直接进行板图设计就可以完成产品应用。
2.2 丛机介绍
目前丛机已经实现了如下产品的开发，省掉了终端的mcu，客户直接设计板卡就可以直接使用。
单火线开关1-4键
零火线开关1-6键
调光灯（色温，亮度可调）
插卡取电
温控器
电动窗帘
门磁
人体红外
水浸传感器
温湿度传感器
烟感传感器
智能插座
门锁
烟感传感器
透传丛机
2.3 应用框图
系统应用框图如下：
客户可以开发自己的网关
 使用串口主机模块，外挂处理器进行开发
 使用我们的网关进行开发（我们网关可以开放内部mcu）
 网关进行定制
2.4 主机模块特点
主机蓝牙模块支持下面特点
 支持105个丛机节点
 支持MESH和BLE共存，支持手机本地BLE连接主机进行mesh网络的管理
 支持32个场景保存网关内部
 支持本地设备的白名单管理
 支持网关替换，参数导出
 支持网关固件升级
 支持单波控制，场景控制（一包控制多个设备）
 支持丛机配置锁定和解锁
 支持开关操作锁定
 支持离线场景设置，无网关也可以本地实现场景控制
3  网关协议介绍
采用串口主机模块进行开发，就涉及到协议对接，下来主要介绍蓝牙网关模块的接口协议。
3.1 串口参数
蓝牙模块对外通过串口进行通信。
波特率:  115200 bps
数  据:  8 bit
停止位:  1 bit
校验位:  None
一般查询命令后 200mS 内反馈应答信号，否则 200mS 后重发，超过三次无应答信号则视为通讯故障 。
3.2 通信协议格式
协议基本格式：
发送格式：头码 + 操作类型码 + 数据长度 + 数据 + 校验码
    响应格式：头码 + 操作类型码 + 响应状态 + 数据长度 + 数据 + 校验码 
发送格式
Head 
Opcode
ParamLength 
ParamData 
Check
头码
(1 字节) 
操作类型码
（ 1 字节） 
数据长度（ 1 字节） 为0时无ParamData 项
数据
（ Length 字节） 
校验(1 字节)
响应格式
Head 
Opcode
Status
ParamLength 
ParamData 
Check
头码
(1 字节) 
操作类型码（ 1 字节） 
命令响应或事件通知（ 1 字节） 
数据长度（ 1 字节） 为0时无ParamData 项
数据
（ Length 字节） 
校验(1 字节)
头码固定值为 0x53.
操作类型码请求类型 0x01-0x70  回复类型 0x81-0xf0
Check 为异或校验 头码^操作类型码^数据长度^[数据]
每条命令都会有命令的响应回复需要等待，异步的事件也会通过对应的操作码和事件主动发送给主机。
注意配置命令如果需要同步节点时，需要等节点反馈事件或超时后。再依次配置。 比如场景配置，同步模式时配置本地双控。
3.3 操作类型码详解
协议格式里面存在操作类型，操作类型码分为下面几类： 
系统命令
配网命令
配置命令
控制命令
升级命令
透传命令
3.3.1 系统命令
系统命令是网关模块内部的一些配置参数的设置。
操作类型码
（发送）
操作类型码
（返回）
命令描述
0x01
0x81
读取/设置网关模块BLE广播名称（蓝牙名称）
0x02
0x82
读取网关模块App软件版本信息
0x03
0x83
读取网关模块Mac地址
0x04
0x84
读取/设置网关模块BLEMesh网络密钥（网络唯一标识）
0x05
0x85
恢复出厂网关模块，清除网关内部网络，场景，设备等所有参数
0x06
0x86
复位网关模块
0x07
0x87
读取/设置网关模块BLE广播是否使能（是否开启蓝牙），广播间隔等
0x09
0x89
读取网关Mesh参数详情
注意： 主机模块支持MESH和BLE共存，通过BLE访问MESH，这个功能也可以不使用。
3.3.2 配网命令
主机模块把丛机配置到网络内部，并且进行管理。
操作类型码
（发送）
操作类型码
（返回）
命令描述
0x10
0x90
扫描待配网设备，发现进入配网的丛机会主动上报
0x11
0x91
退出扫描状态
0x12
0x92
读取/添加设备 白名单列表，白名单内部的才能进行添加
0x13
0x93
删除列表内设备
0x14
0x94
清除主机内的所有节点设备
0x15
0x95
读取节点总个数
3.3.3 配置命令
场景，双控设置
操作类型码
（发送）
操作类型码
（返回）
命令描述
0x20
0xa0
配置场景N下指定设备的目标状态
0x21
0xa1
删除场景N下的目标状态
也可配置无效状态
3.3.4 控制命令
主机对丛机的控制命令，实现设备的开关等控制。
操作类型码
（发送）
操作类型码
（返回）
命令描述
0x30
0xb0
单播控制，指定设备的开/关，亮度，色温等
0x31
0xb1
场景控制，指定场景ID下的设备，动作至配置状态，多设备一起动作
0x32 
0xb2 
网络内节点设备状态主动刷新，异步方式，可群控，节点状态将以事件方式 返回给网关主机
0x33 
0xb3 
开关类的 双控控制，操作指定开关的同时 会同步双控组内的开关
3.3.5 透传命令
主机和透传丛机进行数据透传通信
操作类型码
（发送）
操作类型码
（返回）
命令描述
0x40
0xc0
Mesh网络指定丛机透传模块，使用网络短地址透传通信
0x41
0xc1
BLE GATT连接透传数据，返回数据均以事件类型通知网关主机，手机连接网关，可以和本地mcu进行数据通信
3.3.6 升级命令
网关模块实现固件升级
操作类型码
（发送）
操作类型码
（返回）
命令描述
0x50
0xd0
进入/退出 网关模块升级模式
0x51
0xd1
发送升级文件
3.4  命令响应事件通知
返回命令里面有一个数据字节是： 命令响应或事件通知，这是一个时间类型码。描述如下：
typedef enum
{
RESULT_CMD_NODE_LIST_FULL = -6,        // 节点数目已满
  RESULT_CMD_APP_TX_STATE_LIMITED = -5,  // 发送状态受限 正配网或者升级过程
  RESULT_CMD_APP_TXBUF_FULL = -4,  // 发送队列满
  RESULT_CMD_NOT_SUPPORT =-3,   //命令不支持
  RESULT_CMD_CHECK_ERROR = -2,  //校验值出错
RESULT_CMD_NET_SEND_BUSY = -1, //网络发送忙
  RESULT_CMD_OK = 0,
  RESULT_CMD_ERROR,  //命令参数出错
  RESULT_EVENT_SCAN_PAIR_FOUND,
  RESULT_EVENT_PAIR_STATUS_COMPLETE,
  RESULT_EVENT_PAIR_STATUS_FAIL,
RESULT_EVENT_NODE_ACK,
RESULT_EVENT_NODE_STATUS,    //设备参数值变化通知
RESULT_EVENT_MESH_NODE_TRANS_MSG,
RESULT_EVENT_BLE_GATT_CONNECT,
RESULT_EVENT_BLE_GATT_DISCONNECT,
RESULT_EVENT_BLE_GATT_TRANS_MSG,
RESULT_EVENT_REBOOT_SUCCESS,
RESULT_EVENT_OTA_STATUS, 
RESULT_EVENT_NODE_ACK_TIMEOUT,  //在规定的时间内 一般为1.2S 没有ACK响应 
RESULT_EVENT_SK_GROUP_SYNC_STATUS,  
RESULT_EVENT_NODE_OFFLINE_RPT,  //离线通知事件
RESULT_EVENT_LIST_NODE_UPDATE_RPT,  //节点列表更新事件
} cmd_process_result_t; 
3.5  数据区域说明
协议里面存在一个数据区域，如下
不同命令数据区域内容不同，具体请参考，命令解析章节。
数据区域内部会使用到下面的操作命令，介绍如下：
3.5.1 操作命令总结
下面是对各种设备的操作命令总结。
3.5.1.1 设备通用操作
对所有丛机设备进行的基础操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
DEV_COMMON
SOFT_VERSION
查询/通知
3Byte
丛机设备的软件版本
丛机固件版本
STATUS_CHG_RPT_EN
查询/设置/通知
1Byte
0：禁用
1：使能
使能设备状态主动上报功能
一般用于离线模式
丛机上报使能
3.5.1.2 开关操作
对单火线，零火线开关进行操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
DEV_COMMON
TYPE_ON_OFF
查询/设置/通知
1Byte中2Bit表示1路开关，支持4路
b01表示关状态
b10 表示开状态
其余b00 b11 保留
低2位开始表示第一路开关
TYPE_CTRL_SOURCE
通知
1Byte
0: 未知  1: 本地按键  
2: Mesh网络
3: 私有双控网络
触发类型
PAIR_FUNC_LOCK_CFG
查询/设置/通知
1Byte
0：未锁定
1：锁定
锁定开关按键 除开关外的功能
如进入配网，本地双控配对清除功能等 由网关端统一配置。
注：默认上电两分钟内不锁定
配网锁定
KEYS_1_4_CFG
KEYS_5_6_CFG
查询/设置/通知
KEYS_1_4_CFG  4Byte
KEYS_5_6_CFG  2Byte
每个字节表示对应路开关是启动的哪个场景。
0：   表示普通开关
1-32：表示场景执行开关
场景ID 0-31
      (常用无网关模式 直接发出场景信号，无需网关参与) 
33： 表示只无线开关
(随意贴)功能 
本地按键触发不动作该路负载,一般只作为另一路开关的扩展开关用， 单向绑定，单击按键会切换绑定开关的开关状态。
35:   表示场景按键上报开关，事件为SCENE_PANEL_EVENT, 由网关对应启动设定场景执行。
字节顺序依次为一二三四路。
36:   表示循环场景执行功能
由KEY_LOOP_SCENE_CFG配置对应执行的开始场景ID和结束场景ID 一般用于开关总控功能
可选
普通开关=本地负载控制+无线双控开关。
当某负载路闲置或 已接智能灯等 可设置为：
无线开关（随意贴）模式，无线双控开关，场景开关。
负载路仍可由网关单独控制。
PWR_ON_AUTO_ONOFF_CFG
查询/设置/通知
1Byte
Bit: 0 关闭
Bit：1打开
对应位置位表示需要上电打开对应路开关 从最低位开始表示第一路。
上电状态
KEYS_ON_MUTEX_CFG
查询/设置/通知
1Byte
Bit0-3: 第一组负载互斥设置
Bit4-7: 第二组负载互斥设置（仅4开支持两组设置）
对应bit位置位表示需要打开互斥。常用于立即清理 请勿打扰或窗帘控制面板开关打开 关闭接强电电机。四开可同时用于窗帘和窗纱的控制
打开
互斥
DEV_LOCK_CFG
查询/设置/通知
1Byte
0：未锁定
1：锁定
锁定设备
锁定开关的物理按键和无线控制通道。
注：默认上电两分钟内不锁定
操作锁定
KEY_LOOP_SCENE_CFG
查询/设置/通知
3Byte
Byte0：开关第几路（1-6）
Byte1：0：无效
       1-32 起始场景
Byte2：0：无效
       1-32 结束场景
开关功能设置为循环场景执行时，设置循环场景的起始和结束 开关本地循环执行。一般用于总控，对应的场景里可自定义具体的设备执行的状态
循环场景
3.5.1.3 场景开关
纯场景开关不带负载
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
SCENE_PANEL
SCENE_SWITCH
SCENE_PANEL_EVENT
通知
2Byte 共支持8路场景开关
Byte中2Bit表示1路开关
b01 表示 该路被触发
其余状态保留
高2位开始表示第一路被触发
低字节在前
0x40 0x00表示第1路 
0x00 0x10表示第6路 
依次往后表示1-6路
SCENE_SWITCH情景开关 是 情景面板和开关的混合类型
3.5.1.4 电动窗帘操作
对电动窗帘进行如下操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
SMART_CURTAIN
CURT_RUN_STATUS
查询/设置/通知
1Byte 窗帘运行状态
1:打开 2：关闭 3：停止
其余值保留
CURT_RUN_PER_POS
查询/设置/通知
1Byte 窗帘百分比运行位置
0-100 百分比
0xFF 未知
其余值保留
CURT_CFG_RUN_DIR
查询/设置/通知
1Byte 窗帘运行方向配置
1:正转 2：反转 3：换向
其余值保留
CURT_SW_RCU_CFG
查询/设置/通知
10Byte
Byte0：绑定通道 0-5 支持6个窗帘开关面板
Byte1-6：开关面板Mac地址
Byte7：打开-对应开关路
       0：无效 同不配置
      1-6：开关的对应路
Byte8：停止-对应开关路
      参数同上
Byte9：关闭-对应开关路
这里是设置开关型的 窗帘控制面板。
停止可以不配置 不配置时当窗帘打开时 再收到打开命令就会停止 一般用于双键开关或四键开关。
绑定开关作为遥控
DEV_RCU_CFG
查询/设置/通知
8Byte
Byte0：绑定通道 0-5 支持6个遥控器
Byte1-6：遥控器Mac地址
Byte7：1-N  默认1
多通道时 遥控器通道号
添加专用的窗帘遥控器
遥控器方案翼数提供
3.5.1.5 门磁操作
对所有类型设备进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
DOOR_SENSOR
DOOR_SENSOR_STATUS
通知
1Byte 门磁打开关闭状态
0: 关闭
1: 打开
BATTERY_LEVEL_STATUS
通知
1Byte 电池电量
1-100
其余值无效
TAMPER_ALARM
通知
1Byte 防拆报警
0: 正常
1: 报警
KEYS_1_4_CFG
查询/设置/通知
4Byte 只用前2Byte
Byte0 开门需要执行场景ID  
Byte1 关门需要执行场景ID                                   其余两字节填0  
0 表示默认功能，上报事件到网关
1-32 执行的场景ID
常用于配置离线场景联动
门磁场景
SCENE_ACT_PAR_CFG
查询/设置/通知
4Byte（单位秒）
Byte0：开门场景延时执行时间
Byte1: 开门持续时间                                    Byte2：关门场景延时执行时间
Byte3: 关门持续时间
常用于配置离线场景联动，配置延时执行或者持续时间条件执行
0 为 不配置
持续时间就是开门多久才认为是开门，延迟执行时间就是发现开门后，延迟多久执行场景
TEMP_SLEEP_MODE
通知
2Byte 
Byte0：功耗模式
0：低功耗
1：中功耗
2：高功耗
Byte1：模式维持时间 单位S
当单击门磁配网按键后 功耗模式将切换到高功耗 等待设置 超时后自动恢复为低功耗
功耗状态
3.5.1.6 人体红外操作
对所有类型设备进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
HB_DET
HB_DET_STATUS
通知
1Byte 人体活动状态
0: 无人体活动（30秒内无动作）
1: 检测到人体活动
BATTERY_LEVEL_STATUS
通知
1Byte 电池电量
1-100
其余值无效
LOW_BATTERY_LEVEL_ALARM
通知
1Byte
0: 正常
1: 报警
低电量
KEYS_1_4_CFG
查询/设置/通知
同DOOR_SENSOR内，检测到人活动，或者人不活动场景设置
参考门磁
SCENE_ACT_PAR_CFG
查询/设置/通知
同DOOR_SENSOR内，场景执行延时和持续时间
参考门磁
TEMP_SLEEP_MODE
通知
同DOOR_SENSOR内，传感器功耗状态
参考门磁
3.5.1.7 水浸传感器操作
对水浸传感器进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
WATER_ALARM_SENSOR
SENSOR_WATER_ALARM
通知
1Byte
0: 正常
1: 报警
BATTERY_LEVEL_STATUS
通知
1Byte 电池电量
1-100
其余值无效
3.5.1.8 烟感传感器操作
对烟感传感器进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
SMOKE_ALARM_SENSOR
SENSOR_SMOKE_ALARM
通知
1Byte
0: 正常
1: 报警
BATTERY_LEVEL_STATUS
通知
1Byte 电池电量
1-100
其余值无效
LOW_BATTERY_LEVEL_ALARM
通知
1Byte
0: 正常
1: 报警
3.5.1.9 插卡取电操作
对插卡取电进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
SMART_PLUG_CARD
CARD_DET_STATUS
通知
1Byte 插卡状态
0: 无卡
1: 有卡
TYPE_ON_OFF
查询/设置/通知
同上 
只支持1路开关
KEYS_1_4_CFG
查询/设置/通知
同DOOR_SENSOR内，插卡，拔卡场景设置
参考门磁
SCENE_ACT_PAR_CFG
查询/设置/通知
同DOOR_SENSOR内，插卡，拔卡场景执行延时和持续时间
参考门磁
3.5.1.10 温湿度传感器操作
对水浸传感器进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
TEMPR_HUMI_SENSOR
SENSOR_TEMP
通知
2Byte 摄氏度
*100  精度0.01 C
温度范围 -10 - 50 C
SENSOR_HUMI
通知
1Byte 相对湿度
0-100  1%
BATTERY_LEVEL_STATUS
通知
1Byte 电池电量
1-100
其余值无效
3.5.1.11 调光驱动操作
对水浸传感器进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
SMART_LIGHT
LIGHT_LIGHTNESS
查询/设置/通知
1Byte 亮度百分比
0-100  1%
LIGHT_TEMPERATURE
查询/设置/通知
1Byte 色温百分比
0-100  1%
LIGHT_LIGHTNESS_INC
设置
1Byte 亮度调大
1-100 步长 最大调到100%
步长
LIGHT_LIGHTNESS_DEC
设置
1Byte 亮度调小
1-100 步长 最小调到1%
LIGHT_TEMPERATURE_INC
设置
1Byte 色温调大
1-100 步长 最大调到100%
LIGHT_TEMPERATURE_DEC
设置
1Byte 色温调小
1-100 步长 最小调到0%
DEV_RCU_CFG
查询/设置/通知
同上（窗帘遥控）
添加专用调光遥控器
3.5.1.12 温控器操作
对温控器进行的操作。
设备类型
Dev_type_list
参数属性
Msg_type
操作
operate
参数值
Param[]
说明
TEMPR_CONTROLLER
SENSOR_TEMP
通知
2Byte 摄氏度
*100  精度0.01 C
温度范围 -10 - 50 C
TMPC_TEMP
查询/设置/通知
1Byte 设置温度
一般 5-35 具体范围由具体温控器决定
TMPC_WIND_SPEED
查询/设置/通知
1Byte 设置风速
0: 未设置
1: 高速  2: 中速
3: 低速  4: 自动
TMPC_MODE
查询/设置/通知
1Byte 设置模式
0: 未设置
1: 制冷  2: 制暖
3: 通风
TMPC_KB_LOCK
查询/设置/通知
1Byte 按键锁
0: 解锁
1: 锁定
其余值无效
TMPC_VALVE
查询/通知
1Byte 通气阀开关
0: 关闭
1: 开启
其余值无效
可选
TMPC_SLEEP
查询/设置/通知
1Byte 睡眠
0: 关闭
1: 开启
其余值无效
可选
TMPC_TEMP_INC
设置
1Byte 温度调高
1-N 步长 一般为1 
可选
TMPC_TEMP_DEC
设置
1Byte 温度调低
1-N 步长 一般为1
可选
TMPC_WIND_SPEED_INC
设置
1Byte 风速调高
1-N 步长 一般为1
可选
TMPC_WIND_SPEED_DEC
设置
1Byte 风速调低
1-N 步长 一般为1
可选
IR_ACTION
设置
2Byte   
Byte 0: 0:控制 1：学习	 Byte 1: KeyID 
电视 0-0x21 
机顶盒0x22 - 0x43
红外IR_ACTION KeyID定义
NAME 
电视机 按键 ID
机顶盒 按键 ID
静音 
0
22
睡眠 
1
23
主页 
2
24
显示 
3
25
音量+ 
4
26
频道+ 
5
27
台号 
6
28
返回 
7
29
上页 
8
2A
导航/首页 
9
2B
下页 
0A
2C
电视 
0B
2D
点播 
0C
2E
电源 
0D
2F
音量- 
0E
30
频道- 
0F
31
上 
10
32
下 
11
33
左 
12
34
右 
13
35
确定 
14
36
菜单 
15
37
信号源 
16
38
退出 
17
39
0
18
3A
1
19
3B
2
1A
3C
3
1B
3D
4
1C
3E
5
1D
3F
6
1E
40
7
1F
41
8
20
42
9
21
43
3.5.2 设备类型码
操作命令里面的设备类型总结如下：
设备类型编码如下：
typedef enum
{
DEV_TYPE_UNKOWN = 0,
  DEV_TYPE_ZERO_FIRE_SWITCH = 1,  // 零火开关 包含一路 二路 三路 四路  可设置情景
  DEV_TYPE_SINGLE_FIRE_SWITCH,    //单火开关 包含一路 二路 三路 四路   可设置情景
DEV_TYPE_SMART_SOCKET,         //智能插座
DEV_TYPE_SMART_LIGHT,           //智能灯
DEV_TYPE_SMART_CURTAIN,        //智能窗帘
DEV_TYPE_SCENE_PANEL,          //情景面板 只情景功能
DEV_TYPE_DOOR_SENSOR,         //门磁
DEV_TYPE_HB_DET,                //人体感应
DEV_TYPE_SMART_PLUG_CARD,    //智能插卡取电
DEV_TYPE_TEMPR_CONTROLLER,    //温控器
DEV_TYPE_TEMPR_HUMI_SENSOR,   // 温湿度传感器
DEV_TYPE_SCENE_SWITCH,          //情景开关  开关+情景功能  包含一路 二路 三路 四路 且固定情景个数
//上下微动开关实现 类型固定为 一开一情景，两开两情景，三开三情景，四开两情景。DEV_TYPE_OFFLINE_VOICE_CONTROLLER,  //  离线语控节点
DEV_TYPE_SMART_DOOR_LOCK,    // 门锁
DEV_TYPE_WATER_ALARM_SENSOR,  // 水浸报警
DEV_TYPE_SMOKE_ALARM_SENSOR,  // 烟雾报警
DEV_TYPE_SMART_TV_BOX,        // 智能电视盒子
DEV_TYPE_SINGLE_FIRE_SCENE_SWITCH,  // 单火开关+ 情景包含一路 二路 三路四路 且固定情景个数
//上下微动开关实现 类型固定为 一开一情景，两开两情景，三开三情景，四开两情景。
} support_dev_type_list_t;
3.5.3 参数属性列表
操作命令里面的设备参数属性列表总结如下：
支持开关 开/关 亮度 色温，窗帘开 关 百分比 等。包含通用设备属性的，也包含专有设备属性的消息类型码。对应消息的操作方式有 可读/可写/上报。
typedef enum
{	
VD_MSG_TYPE_ON_OFF = 2,
VD_MSG_TYPE_LIGHT_LIGHTNESS, /* relative value */
VD_MSG_TYPE_LIGHT_TEMPERATURE, /* relative value */
VD_MSG_TYPE_CURT_RUN_STATUS,
VD_MSG_TYPE_CURT_RUN_PER_POS,
VD_MSG_TYPE_SCENE_PANEL_EVENT,
VD_MSG_TYPE_CURT_CFG_RUN_DIR,
VD_MSG_TYPE_CURT_CFG_HAND_START,
VD_MSG_TYPE_DOOR_SENSOR_STATUS,
VD_MSG_TYPE_BATTERY_LEVEL_STATUS,
VD_MSG_TYPE_HB_DET_STATUS,
VD_MSG_TYPE_CTRL_SOURCE = 0x0D,  //0: unkown 1: local  2: mesh net 3: shuangkong net
VD_MSG_TYPE_CARD_DET_STATUS,
VD_MSG_TYPE_SOFT_VERSION,  // 3Byte
VD_MSG_TYPE_STATUS_CHG_RPT_EN_CFG,     // 0: off  1:on 
VD_MSG_TYPE_PAIR_FUNC_LOCK_CFG, //0: unlock func   1: lock func   mesh pair and rcu pair and restore func VD_MSG_TYPE_KEYS_1_4_CFG,    //4Byte  0: normal  1- 32 scene no  etc
VD_MSG_TYPE_TEMP_SLEEP_MODE, //内部协议使用 2Byte  0: restore  1: middle level  2 max  //temp hold seconds
VD_MSG_TYPE_SK_CFG,            //内部协议使用
VD_MSG_TYPE_MESH_TRANS,      //表示Mesh透传格式 参数为透传数据
VD_MSG_TYPE_SENSOR_TEMP = 0x16,  // 2Byte signed *100 0.01 C
VD_MSG_TYPE_SENSOR_HUMI,  // 1Byte 1% 
VD_MSG_TYPE_BACK_LIGHT_CFG,
VD_MSG_TYPE_KEYS_5_6_CFG,  //2Byte 0: normal  1- 32 scene no 
//add 20210518
VD_MSG_TYPE_PWR_ONOFF_SCENE_CFG = 0x1A,  //2Byte	0: normal  1- 32 scene no 
VD_MSG_TYPE_TMPC_TEMP,           // 1Byte  maybe 5-35
VD_MSG_TYPE_TMPC_WIND_SPEED,  // 1Byte
VD_MSG_TYPE_TMPC_MODE,         // 1Byte
VD_MSG_TYPE_TMPC_KB_LOCK,     // 1Byte
VD_MSG_TYPE_TMPC_VALVE,       // 1Byte
VD_MSG_TYPE_H20_RESV = 0x20,
VD_MSG_TYPE_TMPC_SLEEP,      // 1Byte
//add 20210618
VD_MSG_TYPE_NAMEID_1_4_CFG =0x22,   // 4Byte  user define name id
VD_MSG_TYPE_NAMEID_5_6_CFG,         // 2Byte  user define name id
VD_MSG_TYPE_PWR_ON_AUTO_ONOFF_CFG,  // 1Byte  bit set mean on after pwr on
VD_MSG_TYPE_KEYS_ON_MUTEX_CFG,      // 1Byte bit set mean need mutex in byte,  max 2 * 4bits
VD_MSG_TYPE_DEV_LOCK_CFG,           // 1Byte only some customer support 
VD_MSG_TYPE_KEY_LOOP_SCENE_CFG,     // 3Byte  key ch , start  end  scene
VD_MSG_TYPE_CURT_SW_RCU_CFG,        // 1+6+3 Byte  bind ch + rcu mac + key ch open stop close  
VD_MSG_TYPE_DEV_RCU_CFG,            // 1+6+1 Byte bind ch + rcu mac  +  rcu ch 
//add 20210718
VD_MSG_TYPE_SENSOR_WATER_ALARM = 0x2A,  // 1Byte  0: normal  1: alarm
VD_MSG_TYPE_SENSOR_SMOKE_ALARM,         // 1Byte  0: normal  1: alarm
VD_MSG_TYPE_LOW_BATTERY_LEVEL_ALARM,    // 1Byte  0: normal  1: alarm
VD_MSG_TYPE_TAMPER_ALARM,	              // 1Byte  0: normal  1: alarm
//add 20210908
VD_MSG_TYPE_SCENE_DELAY_ACT_CFG = 0x2E,  // 4Byte 
//add 20210930
VD_MSG_TYPE_LIGHT_LIGHTNESS_INC = 0x2F,  // 1Byte   step value 1-100
VD_MSG_TYPE_LIGHT_LIGHTNESS_DEC,        // 1Byte   step value 1-100
VD_MSG_TYPE_LIGHT_TEMPERATURE_INC,      // 1Byte   step value 1-100
VD_MSG_TYPE_LIGHT_TEMPERATURE_DEC,      // 1Byte   step value 1-100
VD_MSG_TYPE_TMPC_TEMP_INC,              // 1Byte   step value
VD_MSG_TYPE_TMPC_TEMP_DEC,              // 1Byte   step value
VD_MSG_TYPE_TMPC_WIND_SPEED_INC,        // 1Byte   step value
VD_MSG_TYPE_TMPC_WIND_SPEED_DEC,        // 1Byte   step value
VD_MSG_TYPE_IR_ACTION = 0x8f,  //参数 2Byte   Byte 0: 0:控制 1：学习	 Byte 1: KeyID 电视 0-0x21 机顶盒0x22 - 0x43
VD_MSG_TYPE_MAX = 0xff
} vd_msg_type_t;
4 命令解析
4.1 系统命令
命令发送
头码 
数据类型
数据长度
数据 
校验
0x53
0x01 
0/9
读取/设置网关模块BLE广播名称
0x02 
0
读取网关模块App软件版本信息
0x03 
0
读取网关模块Mac地址
0x04
0/16
读取/设置网关模块BLEMesh网络密钥
（网络唯一标识）
0x05
0
恢复出厂网关模块
0x06
1
复位网关模块
0x07
0/8
读取/设置网关模块BLE广播是否使能 广播间隔等
0x09
0
读取网关Mesh参数详情
命令反馈
头码 
数据
类型
状态
数据长度 
数据 
校验
0x53
0x81
0x00
(RESULT_CMD_OK)
9
字符串 0000-0000
0x82
0x00
4
01 00 02 00  版本 (1.0.2.0)
0x83
0x00
6
98 06 E2 07 DA 78    
MAC  (78DA07E20698)
0x84
0x00
0x10/0
网络密钥16Byte
0x85
0x00
0
恢复成功
0x86
0x00
0
复位成功
0x87
0x00
0x08/0
网关广播设置信息
0x89
0x00
网关Mesh参数详情
广播的使能和广播间隔参数
typedef struct
{
u8 enable;   //0 不广播 1 广播
u8 interval;  //*100ms  默认10
  u8 rfu[6];      
} _PACKED_  gw_bdcast_cfg_t;
数据实例
//读取BLE NAME，红色是BLE name
53 01 00 52 
53 81 00 09 31 31 30 31 2D 38 33 30 31 FD
//设置BLE NAME 必须9个字节，红色是name
53 01 09 31 31 30 31 2D 38 33 30 31 7D
53 81 00 00 D2 
//读取APP版本
53 02 00 51 
53 82 00 04 01 01 02 00 D7
//读取MAC地址
53 03 00 50 
53 83 00 06 77 32 8C 38 C1 A4 42 
//读取Mesh NetKey
53 04 00 57
53 84 00 10 31 31 30 31 2D 38 33 30 31 D9 DA DB DC DD DE DF 39
//设置Mesh NetKey 必须16个字节  重新设置网络密钥 需要先将网关恢复出厂，每个网络密钥不同
53 04 10 31 31 30 31 2D 38 33 30 31 D9 DA DB DC DD DE DF B9
53 84 00 00 D7
//恢复出厂 命令超时2S
53 05 00 56 
53 85 00 00 D6 
//复位
53 06 00 55
53 86 00 00 D5 
//读取蓝牙广播配置，设置参数参考前面结构体
53 07 00 54 
53 87 00 08 01 0A 00 00 00 00 00 00 D7 
//设置蓝牙广播配置
53 07 08 01 0A 00 00 00 00 00 00 57 
53 87 00 00 D4 
//读取网关详情
53 09 00 5A 
53 89 00 1E 01 1F C7 7D 9C 73 D3 A0 F1 4B A3 D8 3A 3B 7A 63 42 00 00 00 11 22 33 44 05 01 3B 0F 00 00 13 
4.2 配网命令
命令发送
头码 
数据类型
数据长度
数据 
校验
0x53
0x10 
0/6
扫描待配网设备(可指定设置MAC地址)
0x11 
0
退出扫描状态
0x12
0/8
读取/添加设备 白名单列表
网关将会主动发现和添加白名单设备
0x13
6
删除列表内设备 (指定MAC)
0x14
0
清除主机内的所有节点设备
 0x15
0
读取节点总个数
命令返回
头码 
数据类型
状态
数据长度
数据 
校验
0x53
0x90
0x00
(RESULT_CMD_OK)
/(RESULT_EVENT_SCAN_PAIR_FOUND)
0x00/0x10
扫描响应事件内容见结构体
0x91
0x00
0
执行成功
0x92
0x00
/(RESULT_EVENT_PAIR_STATUS_COMPLETE)
/(RESULT_EVENT_PAIR_STATUS_FAIL)
0x00/0x0E/0x10
设置成功或返回设备列表
结构体如下
返回16字节 
自动添加成功和失败事件
结构如下
0x93
0x00
0
执行成功
网关将会强制删除该设备。不论设备是否在线离线
0x94
0x00
0
执行成功
节点删除的同时 节点相关的配置也一并删除
0x95
0x00
1
节点总数
扫描请求： 指定mac进行扫描
typedef struct
{
  u8 mac[6];   //可选项
} _PACKED_  scan_req_t;
扫描结果事件将动态的上报至主机 
typedef struct
{
s8 rssi;           //发现设备信号强度
u8 mac[6];
u16 vendor_id ;   // 支持客户Vendor编码 隔离厂商
u8 dev_type;     // 设备类型
u8 dev_sub_type; // 具体支持几路 1-8路
u8 resv[5];
} _PACKED_  scan_rsp_evt_t; 
设置设备白名单，可以连续传入多组MAC（添加设备）
typedef struct
{
  u8 mac[6];
  u16 naddr;     //添加时传0 表示需要重新配对添加 地址由网关自动分配，添加时不传0是已有设备
} _PACKED_  dev_list_node_t;   //配网成功事件 返回同样结构体
获取设备白名单 可批量返回
typedef struct
{
u8 max;          // 节点总数  最大105个
u8 index;         // 0 - max-1   辅助批量返回列表  命令返回 等到index =max-1  列表返回完毕
u8 mac[6];
u16 naddr;        // 网络短地址 未分配时为0
u16 vendor_id ;   // 支持客户Vendor编码 隔离厂商 0未知
u8 dev_type;     // 设备类型  0未知
u8 dev_sub_type; // 具体支持几路 1-8路  0未知
u8 online:1;      //bit0  0: 离线 1:在线  
u8 only_tmall:1;  // bit1  0: 厂商私有协议  1：只支持天猫精灵协议
u8 status:6;     // 保留状态 bits
u8 resv;        //    
} _PACKED_  dev_list_node_rsp_t; 
数据实例
//开始扫描 (开机默认扫描 主动返回发现节点事件)
53 10 00 43
53 90 00 00 C3
//扫描事件主动上报
53 90 02 10 CC B5 91 B5 7A FA 28 A8 01 0A 00 00 00 00 00 00 87
//停止扫描
53 11 00 42
53 91 00 00 C2 
//读取设备列表  连续返回n个节点
53 12 00 41
53 92 00 10 0A 00 EE AF 59 7A FA 28 13 01 A8 01 02 03 00 00 D1 
53 92 00 10 0A 01 C0 A5 00 63 A7 F8 06 01 A8 01 02 03 00 00 2C 
53 92 00 10 0A 02 76 FE 15 6C 14 18 19 01 A8 01 05 00 01 00 91 
53 92 00 10 0A 03 84 17 56 6C 14 18 1A 01 A8 01 0C 04 01 00 C6 
53 92 00 10 0A 04 9A 85 12 75 60 D4 0F 01 A8 01 0C 03 00 00 BB 
53 92 00 10 0A 05 74 88 12 75 60 D4 12 01 A8 01 07 00 00 00 4C 
53 92 00 10 0A 06 0A B0 59 7A FA 28 16 01 A8 01 02 03 00 00 29 
53 92 00 10 0A 07 EB 84 12 75 60 D4 1E 01 A8 01 04 00 00 00 D2 
53 92 00 10 0A 08 93 D1 32 CA D2 38 1F 01 A8 01 04 00 00 00 30 
53 92 00 10 0A 09 B5 91 B5 7A FA 28 20 01 A8 01 0A 00 01 00 68 
//添加设备 添加设备地址为0 网关会自动添加并分配地址  非0地址 认为是手动导入 ，内部自动分配 
53 12 08 B5 91 B5 7A FA 28 00 00 70 
53 92 00 00 C1 
//发现到对应MAC事件02
53 90 02 10 CF B5 91 B5 7A FA 28 A8 01 0A 00 00 00 00 00 00 84 
//自动添加成功事件03（白名单自动添加）
53 92 03 08 B5 91 B5 7A FA 28 21 01 D3
//节点列表更新事件0x10  数据解析同设备列表返回  方便处理 直接覆盖主机对应的节点列表缓存
53 80 10 10 0A 09 B5 91 B5 7A FA 28 21 01 A8 01 0A 00 01 00 6B 
//删除节点
53 13 06 93 8E 37 7A FA 28 C4
53 93 00 00 C0
//读取设备节点总数
53 15 00 46 
53 95 00 01 0A CD
4.3 配置命令
下来主要介绍场景配置。
4.3.1 场景配置命令
配置命令用来对配上网络的对应设备设置一些参数，比如场景命令，主要用来实现对应场景命令发布后，在该场景下的设备执行预置的动作，达到一键同时全控体验。
该设置命令若没有回复时说明该设备不支持该类命令，主机或服务器执行对应场景时 需要单独控制该设备 以实现场景需求。
场景ID列表如下：默认全开 全关场景不用配置 。
命令发送
头码 
数据类型
数据长度
数据 
校验
0x53
0x20 
4+n
配置场景N下指定设备的目标状态
结构体如下：
0x21 
3
删除场景N下的目标状态
命令反馈
头码 
数据类型
状态
数据长度
数据 
校验
0x53
0xa0
0x00/
RESULT_EVENT_NODE_ACK
0/2
操作确认
对应短地址设置成功
0xa1
0x00/
RESULT_EVENT_NODE_ACK
0/2
操作确认
对应短地址设置成功
typedef enum
{
  MESH_SCENE_NO_ALL_OFF = 0,
	MESH_SCENE_NO_ALL_ON,	
  MESH_SCENE_NO_BRIGHT_MODE, //以下为自定义场景名称
	MESH_SCENE_NO_READING_MODE,
	MESH_SCENE_NO_FILM_VIEW_MODE,
	MESH_SCENE_NO_SLEEP_MODE,
	MESH_SCENE_NO_NIGHT_MODE,
	MESH_SCENE_NO_MAX = 32,
} mesh_scene_no_t;
添加设备至场景请求：
typedef struct
{
u16 naddr;
  u8 scene_id;  //场景ID  后面可组合多个msg_type和param  比如设置 调光 和 温控场景时 。
u8 msg_type;   //对应设备的消息码 比如开关控制   参考3.5.3参数属性列表  
u8 param[n];   //需要带参的控制时传入 与msg_type对应长度的参数   
//多路开关 某一路使用两个bit表示开关状态0b01（0x01）表示 0b10(0x02)表示开 其余值无效  一个字节包含4路开关状态
} _PACKED_  dev_cfg_scene_add_req_t;
删除对应设备的场景：
typedef struct
{
u16 naddr;
  u8 scene_id;  //场景ID
} _PACKED_  dev_cfg_scene_delete_req_t;
数据实例
//场景配置  配置节点0x011A 在场景ID2下 全开状态
53 20 05 1A 01 02 02 AA C7
53 A0 00 00 F3
//节点配置成功 返回ACK事件
53 80 05 06 1A 01 00 02 02 AA 61  // 00 为内部使用 场景配置msg_type = 0
//场景配置  配置节点0x011F 在场景ID2下 调光灯 打开 亮度 51%  色温88%
53 20 09 1F 01 02 02 02 03 33 04 58 1F
53 A0 00 00 F3 
53 80 05 08 1F 01 00 02 02 02 03 33 F2   // 注意反馈存在大包情况 只返回设置内容的 部分数据 兼容处理认为成功。
//刷新节点0x011A 内部对应场景ID2的配置
//直接使用查询命令 组装指令
53 32 04 1A 01 00 02 7C 
53 B2 00 00 E1 
//节点返回场景配置
53 80 05 06 1A 01 00 02 02 AA 61  // 00 为内部使用 场景配置msg_type = 0  后面是 场景ID 
//场景控制   激活场景ID2
53 31 01 02 61
53 B1 00 00 E2 
//调光灯执行场景后 引起状态上报
53 80 06 08 1F 01 02 02 03 33 04 58 AF 
//开关执行场景后 引起状态上报
53 80 06 04 1A 01 02 AA 62 
//删除节点0x011A 场景ID2配置
53 21 03 1A 01 02 68 
53 A1 00 00 F2 
//节点操作成功 返回ACK事件
53 80 05 06 1A 01 00 02 FF 00 36 
//组播删除场景ID2下的配置
53 20 05 bc c0 02 FF 00 F7   //使用添加0xFF msg_type  清除节点保存的场景
53 A0 00 00 F3 
53 21 03 bc c0 02 0F 
53 A0 00 00 F3
4.4 控制命令
不同的设备包含不同的参数属性，对应的参数属性包含对应的参数定义。比如开关设备包含开关属性，开关属性包含2个参数开启状态 和 关闭状态 两个值。对于参数属性的操作 一般有查询(可读)，设置（可写），通知。
设备属性表请参考，前面3.5.3表
数据发送：
头码 
数据类型
数据长度
数据 
校验
0x53
0x30 
3（短地址+type）+n
设置： 
指定设备的单播控制 
指定设备的开/关 亮度 色温等参数属性设置
0x31 
1
设置：
场景控制 群控 指定场景ID
同时控制(场景内设备)的开关 亮度 色温等
0x32
2/3
查询：
查询网络内节点设备的可读状态
异步查询方式  可群控
节点状态 将以事件方式 返回给网关主机
命令反馈
头码 
数据类型
状态
数据
长度
数据 
校验
0x53
0xb0
0x00
/RESULT_EVENT_NODE_ACK
0/2
操作确认
对应短地址设置成功失败
0xb1
0x00
0/2
操作确认
群控无返回, 以设备主动更新状态为准
0xb2
0x00
/RESULT_EVENT_NODE_STATUS
0/3+n
网络设备的状态返回与主动更新
控制设备请求：
typedef struct
{
u16 naddr;     //群控时 需要传入0xc0bc 地址
u8 msg_type;   //对应设备的消息码 比如开关控制    参考参数属性表3.5.3
u8 param[4];  //需要带参的控制时传入 具体长度与msg_type对应 一般最大4B
} _PACKED_  dev_normal_ctrl_req_t;  //同 主动上报设备状态事件RESULT_EVENT_NODE_STATUS的数据结构
                                  //设备的实际开关状态发生改变时 会上报NODE_STATUS事件
 控制命令 使用队列发送 接口返回正常时表示入队列成功
 控制成功时 会有NODEACK事件返回 
 控制可能失败时 会有 NODEACK TIMEOUT事件 表示在规定时间内未收到ACK 此时设备可能未控制成功 
设备受控但未反馈 ACK一般发生概率很小 如果出现 提示客户 请将设备移进网关或增加中继设备
 NODE_STATUS 数据实例
53 80 06 06 07 01 02 01 0D 01 DA         //返回多个msg_type和参数的 组合 具体对应参数长度 见：参数属性表3.5.3
02 01 表示 msg_type=02    param = 01  即开关状态 第一路关闭状态
0D 01 表示 msg_type=0D   param = 01  即开关动作是本地按键触发  具体见参数属性表3.5.3
NODE_ACK 数据实例
53 80 05 02 07 01 D2            //旧设备固件版本   只返回短地址
 该事件 也可用来表示开关动作源为 Mesh网络。
53 80 05 02 07 01 02 01 D0             //较新设备固件版本 返回短地址+msg_type和msg参数  主机需要兼容
                                 
场景控制请求：
typedef struct
{
u8 scene_id;  //场景ID
} _PACKED_  dev_scene_ctrl_req_t;
刷新设备状态请求：
typedef struct
{
u16 naddr;     //群控时 需要传入0xc0bc 地址  单独获取 状态返回事件时间3S内  群控返回时间6S内
u8 msg_type;  //可选  不指定 默认设备的所有可读参数 上报
} _PACKED_  dev_status_ctrl_req_t;
指定msg_type 也可查询设备的软件版本 运行模式等信息 具体返回解析参考参数属性表3.5.3
若返回只有msg_type没有参数时 表示设备不支持该类型 或者 设备当前版本不支持 
//获取设备版本 0F为软件版本属性参数
//若设备不支持0F 将不返回参数内容
53 32 03 0F 01 0F 63 
53 B2 00 00 E1 
53 80 05 06 0F 01 0F 01 05 07 D2  
//关闭1-4路开关
53 30 04 00 01 02 55 31  链接
53 B0 00 00 E3   //发送成功
53 B0 FF 00 1C  //发送失败 网络忙 200ms后重试 发送最大1.2S后超时
//节点操作成功 返回ACK事件  组播时无ack 通过上报状态更新设备状态
53 80 05 02 00 01 D5
//开关状态上报   实际当前开关状态发生变化时上报
53 80 06 04 00 01 02 15 C7
//设置开关0x011A功能 1-4 路 msgtype 0x12 第一路普通开关  第二路普通开关  第三路场景ID4执行开关 第四路场景ID5执行开关
53 30 07 1A 01 12 00 00 05 06 76 
53 B0 00 00 E3 
53 80 05 07 1A 01 12 00 00 05 06 DB 
//读取开关0x011A功能 1-4 路 msgtype 0x12
53 32 03 1A 01 12 75
53 B2 00 00 E1 
53 80 05 07 1A 01 12 00 00 05 06 DB 
//设置开关0x011A功能 5-6 路 msgtype 0x19 第五路场景ID0（全关）执行开关  第五路场景ID1（全开）执行开关
53 30 05 1A 01 19 01 02 6C
53 B0 00 00 E3 
53 80 05 05 1A 01 19 01 02 D2 
//读取开关0x011A功能 5-6 路 msgtype 0x19
53 32 03 1A 01 19 75
53 B2 01 00 E0 
53 80 05 05 1A 01 19 01 02 D2 
// 上电打开 msg_type 0x24  上电自动打开H1 H2  （bit0 bit1）
53 30 04 7A 01 24 03 3B
53 B0 00 00 E3 
53 80 05 04 7A 01 24 03 8E 
// 打开互斥 msg_type 0x25  H1 H2打开互斥（bit0 bit1）    H3 H4打开互斥（bit6 bit7）  未选中对应bit位为0
53 30 04 7A 01 25 C3 FA 
53 B0 00 00 E3 
53 80 05 04 7A 01 25 C3 4F 
// 配网锁定 msg_type 0x11  锁定打开 （置1）
53 30 04 7A 01 11 01 0C 
53 B0 00 00 E3 
53 80 05 04 7A 01 11 01 B9 
// 设备锁定 msg_type 0x26  锁定打开 （置1）
53 30 04 7A 01 26 01 3B 
53 B0 00 00 E3 
53 80 05 04 7A 01 26 01 8E 
// 循环场景 msgtype 0x27  H2 开始场景ID 5  结束ID 6 （注意和场景执行一样 ID 需要+1 传入）
53 30 06 7A 01 27 01 06 07 39 
53 B0 00 00 E3 
53 80 05 06 7A 01 27 01 06 07 8C 
//相对调光 
//调光增   msgtype 0x2F  增加值 10%  调光最大到100%
53 30 04 1E 01 2F 0A 5D 
53 B0 00 00 E3 
53 80 05 04 1E 01 2F 0A E8 
53 80 06 08 1E 01 02 02 03 47 04 49 CB 
//调光减   msgtype 0x30  减少值 10%  调光最小到1%
53 30 04 1E 01 30 0A 42
53 B0 00 00 E3 
53 80 05 04 1E 01 30 0A F7 
53 80 06 08 1E 01 02 02 03 3D 04 49 B1
//调色增   msgtype 0x31  增加值 10%  调色最大到100%
53 30 04 1E 01 31 0A 43 
53 B0 00 00 E3 
53 80 05 04 1E 01 31 0A F6 
53 80 06 08 1E 01 02 02 03 64 04 53 F2 
//调色减   msgtype 0x32  减少值 10%  调色最小到0%
53 30 04 1E 01 32 0A 40 
53 B0 00 00 E3 
53 80 05 04 1E 01 32 0A F5 
53 80 06 08 1E 01 02 02 03 5A 04 5A C5
//打开窗帘
53 30 04 01 01 05 01 63 
53 b0 00 00 e3 
53 80 05 04 01 01 05 01 d6 
命令解析
53 30 04 01 01 05 01 63 
53 		//头
30 		//数据类型
04 		//长度
01 01 	//短地址
05 		//VD_MSG_TYPE_CURT_RUN_STATUS 控制开关停
01 		//1:打开 2：关闭 3：停止
63 		//校验
53 b0 00 00 e3 
53 		//头
b0 		//数据类型
00 		//状态
00 		//长度
e3 		//校验
53 80 05 04 01 01 05 01 d6 
53 		//头
80 		//数据类型
05 		//RESULT_EVENT_NODE_ACK
04 		//长度
01 01 	//短地址
05 		//VD_MSG_TYPE_CURT_RUN_STATUS 控制开关停
01 		//1:打开 2：关闭 3：停止
d6 		//校验
//温控器/万能遥控器
//空调打开/关闭
53 30 04 2B 01 02 02 4D /  53 30 04 2B 01 02 01 4E
//空调制冷模式
53 30 04 2B 01 1D 01 51
//空调温度24度
53 30 04 2B 01 1B 18 4E
//打开电视
53 30 05 2B 01 8F 00 0D CE
//打开机顶盒
53 30 05 2B 01 8F 00 2F EC
4.5 透传命令
支持BLEMesh组网下的透传数据服务，从机透传设备在配网后可以通过短地址进行透传数据服务。
支持BLE GATT直连下的透传数据服务主要用来手机辅助配置主机需要参数比如无线网络名称密码，服务器地址等信息，具体内容格式由客户自定义，数据包长度暂定下行最大64Byte(安卓 IOS可设置MTU 64B), 上行20Byte 大于该长度需要自行分包发送。
命令发送
头码 
数据类型
数据长度
数据 
校验
0x53
0x40 
3+n
BLEMesh网络 指定设备网络短地址透传
0x41 
n
BLEGATT连接 透传数据 
返回数据 均以事件类型 通知网关主机
命令反馈
头码 
数据类型
状态
数据
长度
数据 
校验
0x53
0xc0
0x00
/RESULT_EVENT_NODE_ACK
/RESULT_EVENT_MESH_NODE_TRANS_MSG
0/2+n
操作确认
透传数据返回
0xc1
0
/RESULT_EVENT_BLE_GATT_CONNECT,
/RESULT_EVENT_BLE_GATT_DISCONNECT
/RESULT_EVENT_BLE_GATT_TRANS_MSG,
0/n
操作确认
透传数据返回
BLEMesh下行数据发送：
typedef struct
{
u16 naddr;     //群控时 需要传入0xc0bc 地址 建议使用单播 频繁组播会降低网络性能
u8  ack;       //是否需要ACK 单播时有效  建议使用非ACK 返回直接由从机上行数据 确认
u8 param[n]    //透传数据 
} _PACKED_  dev_mesh_send_req_t;
//上行数据事件 RESULT_EVENT_MESH_NODE_TRANS_MSG。数据结构体同dev_mesh_send_req_t
注意从机Mesh透传命令时  不需要dev_mesh_send_req_t数据头
数据实例
53 40 06 29 01 00 31 32 33 0D //网关下行数据31 32 33  至节点0x0129 不需要ACK
53 C0 00 00 93
53 C0 07 03 31 32 33 A7     //对应节点从机 收到数据事件 31 32 33
53 40 06 31 32 33 34 35 36 12  //从机发送响应上行数据 31 32 33 34 35 36
53 C0 00 00 93 
53 C0 07 09 29 01 00 31 32 33 34 35 36 B2 //网关收到从机上行数据时间
4.6 升级命令
通过主机来升级网关模块的固件 以满足Bug修复和新增功能的需求。
命令发送
头码 
数据类型
数据长度
数据 
校验
0x53
0x50 
0
开始/停止 网关模块升级模式
0x51 
2+n*16
发送升级文件 
命令反馈
头码 
数据类型
状态
数据长度
数据 
校验
0x53
0xd0
0
0
接收并操作成功确认
0xd1
0
0
接收成功确认
//开始升级数据部分  开始命令的返回超时3S 开始命令进行了Flash预擦写动作。
  //停止升级数据部分 当文件发送完成或中途需要停止升级时发送 
typedef struct
{
    u8  cmd;      // 1 开始升级    2停止升级 
} _PACKED_  dev_ota_ctrl_req_t;
//发送升级文件 
typedef struct
{
    u16  seq;      // 包序列 从0开始
u8   data[128];   //升级文件内容 支持16B的倍数  默认128B  最后一包数据需是16的整数 不足补0      
} _PACKED_  dev_ota_snd_req_t;
升级流程：
1. 发送开始命令 等待命令的正确响应 超时3S
2. 发送升级文件 以128B每包为例 每包延时20ms 实际升级速率可达5-6KB/S. 
3. 发送包处理有任何异常 将以事件通知  用来反馈控制发送升级包行为，状态返回异常 则停止发送。
只要无异常反馈，发送延时可再进行减小,以各平台测试为准。
4. 文件包发送完成后，发送结束升级命令，校验完成后 反馈升级成功事件 
为了提高升级速度 发送文件被成功接收时将不返回0xd1命令状态，升级时有任何异常（文件不符，升级包序列出错）将通过事件形式RESULT_EVENT_OTA_STATUS通知具体状态如下，升级过程中出错后 模块将自动4S后 重启，主机无需操作。升级成功后将立即重启。主机只要将对应升级结果事件通知服务器即可。
如果需要重新升级 请重复上述流程。
//升级状态类型
 enum {
OTA_SUCCESS = 0,     //success
OTA_PACKET_LOSS,     //lost one or more OTA PDU
OTA_DATA_CRC_ERR,     //data CRC err
OTA_WRITE_FLASH_ERR,  //write OTA data to flash ERR
OTA_DATA_UNCOMPLETE,  //lost last one or more OTA PDU
OTA_TIMEOUT, 		       //no send correct seq data  30S timeout
OTA_OVERFLOW,		       
OTA_ERR_STS,
OTA_SUCCESS_DEBUG,     //success
OTA_FW_CHECK_ERR,
};
5 网关编程指导
5.1  网关应用需求分析
使用翼数设计的网关，可以实现使用mesh网络来控制所有智能设备，并且网关可以连接到自己的云平台，可以实现自己的智能家居全套系统。
目前使用网关初级简单使用需要实现的功能如下：
 使用网关添加丛机设备
 网关删除丛机设备
 管理网络内设备
 网络内配置场景，以及离线场景
 网关对设备进行控制和状态查询
 接收设备通知
 网关执行场景
如果网关实现上述功能就可以实现一套蓝牙mesh的智能系统。 
5.2  操作指导流程
下面主要介绍如何编程，流程如何实现。
5.1  主机如何添加设备
主机上电后初始化串口，让主机进行丛机扫描状态，如果发现到进入配网状态设备，就可以进行添加。
主机上可以增加一个按键，来触发配网，和结束配网。
开始
​
串口初始化
​
是否扫描到新设备
否
是
结束
主机进入配网状态
添加设备到白名单
是否结束添加设备
​
​
​
蓝牙主机进入配网状态，一旦
发现配网状态的丛机，就会上
报，外部mcu可以添加设备到
白名单，同时同步设备表在mcu
内部。
5.2  主机如何删除设备
网关的外部mcu自己会保存设备列表，主机模块也会保存设备列表，两边列表需要进行同步。
网关获取设备列表，在网关外部mcu内部进行管理，之后按照列表内的设备进行删除。
开始
​
获取节点数量
结束
获取设备列表
按照mac进行删除
​
​
外部mcu更新自己的设备表
​
​
删除设备，需要在蓝牙主机列表里面进行删除，
同时mcu外部保存的设备列表也需要进行同步
删除

5.3  接收设备通知
主机mesh模块可以接收到丛机的数据，以及丛机的状态。主机收到后需要进行相应的解析和动作。
开始
​
使能设备通知
（默认打开）
结束
开关状态反馈
（负载开，关）
开关触发状态反馈
（本地触发，mesh触发）
​
​
设备报警
（开关门，有人无人）
​
设备低电量
（门磁，红外）
​
​
对应状态处理
​
设备通知分为很多类型的通知，开关，温控
，插卡，调光都会返回状态，这些状态需要
进行相应动作
5.4  主机如何设置场景
场景会产生一个场景ID，以及这个ID对应的执行动作。 开关收到这个场景ID时会触发场景进行动作。
开始
​
对应设备设置场景ID，设置动作
结束
多个设备设置场景
​
​
设置场景就是设置一个场景ID，同时场景ID放在
被执行设备上，并且描述好执行场景的动作
5.5  双控如何实现
双控的实现：双控可以使用场景来实现，设置一个双控开场景，再设置一个双控关，把双控放在多个开关上，就可以实现双控。
开始
​
结束
设置双控关场景
​
​
​
开关设置为
双控开关
设置双控开场景
开关分为下面几类
1：   普通开关：带负载的普通开关
2：   无线开关，负载不动，发送无线命令的开关
3：   双控开关，开关可以绑定两个场景，切换执行，实现双控
4：   循环场景开关，一个按键，循环发送场景命令

双控就是设置多个双控开关，并且设置好双控场景，最后把双控
两个场景放在这个开关上。

5.6  离线场景如何设置
离线场景的实现：是由本地开关发送场景指令。
开始
​
设置开关为场景开关
（同时设置场景ID）
结束
设置场景执行动作
对应场景ID
​
​
离线场景，就是没有网关时，由本地开关触发的场景
1.  先设置好场景
2.  再设置场景开关，场景开关的场景ID对应设置好的场景
5.7  网关如果控制设备，发送场景命令
网关制定开关或者其他设备执行动作，或者发送预先设置好的场景。
开始
​
指定短地址
控制设备
结束
​
发送场景命令
 返回是否成功
是
否
设备离线
​
 返回是否成功
是
​
否
设备控制可以进行重试，如果超时无返回，可能设备离线
场景命令也会返回，返回失败可以再重试
5.8  配网锁定，设备锁定
主机可以指定设备，进行配网锁定，以及设备锁定
开始
​
指定设备短地址
发送配网锁定
结束
指定短地址
发送设备锁定
​
​
安装设备后以防酒店用户误触发配网，或者改变配置，
可以发送命令让设备锁定配网

如果收不到尾款，可以发送锁定设备命令，使设备本地
无法操作

5.3  整体流程图
下面是一个整体的流程图，最终应用可以参考下面设计。
开始
​
串口初始化
​
设备全部配网
设置场景
设置双控
​
设备控制
场景控制
配置锁定
​
设备通知解析
​
​
​
设备进入配网状态
主机发现丛机，并且添加进入白名单
设置场景ID，场景动作
设置离线场景
设置双控开关，双控场景
丛机配网锁定
传感器开关反馈解析
告警解析
执行动作成功失败解析
场景开关动作触发
按照网络短地址进行控制
按照场景ID进行控制
6 透传节点命令格式
如果存在一些不知道型号的设备类型，可以实行透传方式和设备进行通信。 通信命令格式如下。
//小度透传从机命令 
//上电复位通知事件
53 80 0B 00 D8 
//读取APP版本
53 02 00 51 
53 82 00 04 01 01 02 00 D7
//读取MAC地址
53 03 00 50 
53 83 00 06 26 2D 98 29 5D 3C 0D 
//恢复出厂 命令超时2S
53 05 00 56 
53 85 00 00 D6 
//复位
53 06 00 55
53 86 00 00 D5 
//读取从机类型   //返回PID A9 02 00 00   = 681  透传节点类型
53 35 00 66 
53 B5 00 04 A9 02 00 00 49 
//设置从机类型   // 具体通用品类 PID 联系翼数 获取。 设置成功后 需复位重启。
53 35 04 A9 02 00 00 C9
53 B5 00 00 E6 
//进入扫描发现状态
53 10 00 43 
53 90 00 00 C3 
//退出扫描发现状态
53 11 00 42 
53 91 00 00 C2 
//收到网关或其他节点透传数据(31 32 33)事件 带对方短地址
53 C0 07 05 99 00 31 32 33 38 
//收到网关或其他节点的标准数据点事件 带对方短地址  （需要单独的版本支持）
//可用来实现对应数据点功能，以支持公版类型产品。 实现公版产品上的拓展。  网关下发时可使用0x34命令 noack 方式设置。
53 80 06 04 99 00 02 55 1f
//上行至网关 透传 0x31 32 33
53 40 03 31 32 33 20 
53 C0 00 00 93 
控制设备请求：
typedef struct
{
	u16 naddr; 
	u8  ack;          // 0：不需要ACK 固定
	u8  retrans_cnt;  // 0 - 5     trans_time = (retrans_cnt + 1)*200ms  
	u8  msg_type; 
	u8  param[n];    //与 msg_type 对应的参数长度  可多个msg_type 拼接设置
} dev_normal_ctrl_req_v2_t; 
//发送数据给其他透传节点 透传msgtype = 0x15
//控制其他节点时  按照节点的消息类型码组包
53 34 07 06 01 00 00 15 12 34 24
53 B4 00 00 E7 
//设置需要节点响应时 节点会回复
53 80 05 03 06 01 15 xx
//执行场景  地址使用0xc0bc 考虑成功率 重传5次 持续1.2S   
//场景执行 msgtype = 0x01  场景ID 0x02
53 34 06 bc c0 00 05 01 02 1B
53 B4 00 00 E7
//网关下发场景配置  配置透传节点0x0161 在场景ID2下 输出0x12 0x34 0x56  0x78
53 20 08 61 01 02 15 12 34 56 78 04
53 A0 00 00 F3
//节点配置成功 返回ACK事件
53 80 05 08 61 01 00 02 15 12 34 56 D9 
//场景配置添加通知事件 RESULT_EVENT_NET_SCENE_ADD_RPT = 0x12,   // 透传从机使用
//addr + scene no + par
 53 80 12 07 99 00 02 12 34 56 78 55 
 //场景配置添删除通知事件 RESULT_EVENT_NET_SCENE_DELETE_RPT = 0x13,	  // 透传从机使用
//addr + scene no
53 80 13 03 99 00 02 58 
//网络内场景执行通知事件  RESULT_EVENT_NET_SCENE_ACT_NO_RPT = 0x11 
53 80 11 03 99 00 02 5a 
//收到场景执行数据
53 c0 07 06 99 00 12 34 56 78 03 