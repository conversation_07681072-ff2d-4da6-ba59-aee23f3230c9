"""DataUpdateCoordinator for Symi Mesh Gateway."""
from __future__ import annotations

import asyncio
import logging
from datetime import timed<PERSON><PERSON>
from typing import Any, Callable

from homeassistant.config_entries import ConfigEntry
from homeassistant.const import CONF_HOST, CONF_PORT
from homeassistant.core import HomeAssistant
from homeassistant.helpers.storage import Store
from homeassistant.helpers.update_coordinator import DataUpdateCoordinator, UpdateFailed

from .const import (
    CONF_TIMEOUT,
    DEFAULT_SCAN_INTERVAL,
    DOMAIN,
)
from .device_manager import SymiDeviceManager
from .protocol import SymiDevice
from .tcp_comm import SymiTCPConnection

_LOGGER = logging.getLogger(__name__)


class SymiGatewayCoordinator(DataUpdateCoordinator):
    """Coordinator for Symi Mesh Gateway."""

    def __init__(self, hass: HomeAssistant, entry: ConfigEntry) -> None:
        """Initialize coordinator."""
        self.entry = entry
        self.host = entry.data[CONF_HOST]
        self.port = entry.data[CONF_PORT]
        self.timeout = entry.data[CONF_TIMEOUT]

        # Initialize storage for device persistence
        self._store = Store(
            hass,
            version=1,
            key=f"{DOMAIN}_{entry.entry_id}_devices",
            encoder=self._encode_device_data,
            decoder=self._decode_device_data
        )

        # Initialize connection and device manager
        self.connection = SymiTCPConnection(self.host, self.port, self.timeout)
        self.device_manager = SymiDeviceManager(self.connection)

        # Track devices and their states
        self.devices: dict[str, SymiDevice] = {}
        self._device_states: dict[str, dict[str, Any]] = {}
        self._status_callbacks: dict[str, list[Callable]] = {}
        self._devices_loaded = False

        super().__init__(
            hass,
            _LOGGER,
            name=DOMAIN,
            update_interval=timedelta(seconds=DEFAULT_SCAN_INTERVAL),
        )

        # Register gateway device after super().__init__ so self.hass is available
        self._register_gateway_device()

        # Register for device status updates
        self.device_manager.add_status_callback(self._handle_status_update)

    def _encode_device_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Encode device data for storage."""
        encoded_devices = {}
        for device_id, device in data.get("devices", {}).items():
            encoded_devices[device_id] = {
                "mac_address": device.mac_address,
                "network_address": device.network_address,
                "device_type": device.device_type,
                "device_subtype": device.device_subtype,
                "rssi": device.rssi,
                "vendor_id": device.vendor_id,
                "extended_data": device.extended_data.hex() if device.extended_data else "",
            }

        return {
            "devices": encoded_devices,
            "device_states": data.get("device_states", {}),
            "version": 1,
            "host": self.host,
            "port": self.port,
        }

    def _decode_device_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Decode device data from storage."""
        from .protocol import SymiDevice

        devices = {}
        for device_id, device_data in data.get("devices", {}).items():
            try:
                device = SymiDevice(
                    mac_address=device_data["mac_address"],
                    network_address=device_data["network_address"],
                    device_type=device_data["device_type"],
                    device_subtype=device_data["device_subtype"],
                    rssi=device_data["rssi"],
                    vendor_id=device_data["vendor_id"],
                    extended_data=bytes.fromhex(device_data.get("extended_data", "")),
                )
                devices[device_id] = device
            except Exception as err:
                _LOGGER.warning("Failed to decode device %s: %s", device_id, err)

        return {
            "devices": devices,
            "device_states": data.get("device_states", {}),
        }

    async def _load_stored_devices(self) -> bool:
        """Load devices from storage."""
        try:
            stored_data = await self._store.async_load()
            if stored_data and stored_data.get("host") == self.host and stored_data.get("port") == self.port:
                self.devices = stored_data.get("devices", {})
                self._device_states = stored_data.get("device_states", {})

                # Update device manager with loaded devices
                self.device_manager.devices = dict(self.devices)
                self.device_manager.device_states = dict(self._device_states)

                _LOGGER.info("Loaded %d devices from storage", len(self.devices))
                return True
            else:
                _LOGGER.debug("No valid stored device data found")
                return False

        except Exception as err:
            _LOGGER.warning("Failed to load stored devices: %s", err)
            return False

    async def _save_devices(self) -> None:
        """Save devices to storage."""
        try:
            data = {
                "devices": self.devices,
                "device_states": self._device_states,
            }
            await self._store.async_save(data)
            _LOGGER.debug("Saved %d devices to storage", len(self.devices))

        except Exception as err:
            _LOGGER.error("Failed to save devices: %s", err)

    def _register_gateway_device(self) -> None:
        """Register the gateway device itself."""
        from homeassistant.helpers import device_registry as dr
        
        device_registry = dr.async_get(self.hass)
        device_registry.async_get_or_create(
            config_entry_id=self.entry.entry_id,
            identifiers={(DOMAIN, self.entry.entry_id)},
            name=f"Symi Gateway ({self.host})",
            manufacturer="Symi",
            model="Mesh Gateway",
            sw_version="1.0",
        )

    async def _async_update_data(self) -> dict[str, Any]:
        """Update data via library."""
        try:
            # Load stored devices on first run
            if not self._devices_loaded:
                await self._load_stored_devices()
                self._devices_loaded = True

            # Check connection status
            if not self.connection.connected:
                _LOGGER.info("Connecting to gateway at %s:%s", self.host, self.port)
                if not await self.connection.connect():
                    # If connection fails but we have stored devices, use them
                    if self.devices:
                        _LOGGER.warning("Using stored device data due to connection failure")
                        return {
                            "devices": self.devices,
                            "states": self._device_states,
                            "connected": False,
                            "using_stored_data": True
                        }
                    raise UpdateFailed("Failed to connect to gateway")

                # Discover devices after initial connection (or if no stored devices)
                if not self.devices:
                    await self._discover_devices()

            # For subsequent updates, just return current state
            # Device discovery is only done once or when connection is re-established
            return {
                "devices": self.devices,
                "states": self._device_states,
                "connected": self.connection.connected,
                "last_update": self.last_update_success_time
            }

        except UpdateFailed:
            # Re-raise UpdateFailed as-is
            raise
        except Exception as err:
            _LOGGER.error("Error updating data: %s", err)
            # Try to reconnect on next update
            if self.connection.connected:
                await self.connection.disconnect()
            raise UpdateFailed(f"Error communicating with gateway: {err}") from err

    async def _discover_devices(self) -> None:
        """Discover devices and update registry."""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                _LOGGER.info("Discovering devices (attempt %d/%d)...", retry_count + 1, max_retries)

                # Send device discovery command and wait for responses
                discovered_devices = await self.device_manager.discover_devices()

                if discovered_devices:
                    # Update coordinator devices from device manager
                    self.devices = dict(self.device_manager.devices)
                    self._device_states = dict(self.device_manager.device_states)

                    # Log device discovery results by platform
                    platform_counts = {}
                    for device in self.devices.values():
                        platform = device.platform or "None"
                        platform_counts[platform] = platform_counts.get(platform, 0) + 1
                        _LOGGER.info("Device discovered: %s (%s) -> Platform: %s",
                                   device.mac_address, device.device_name, platform)

                    _LOGGER.info("Device discovery complete. Total devices: %d, By platform: %s",
                                len(self.devices), platform_counts)

                    # Trigger entity creation for newly discovered devices
                    await self._trigger_entity_creation()
                    return
                else:
                    _LOGGER.warning("No devices discovered on attempt %d", retry_count + 1)

            except Exception as err:
                _LOGGER.error("Error discovering devices on attempt %d: %s", retry_count + 1, err)

            retry_count += 1
            if retry_count < max_retries:
                _LOGGER.info("Retrying device discovery in 2 seconds...")
                await asyncio.sleep(2)

        _LOGGER.error("Device discovery failed after %d attempts", max_retries)

    async def _trigger_entity_creation(self) -> None:
        """Trigger entity creation for discovered devices."""
        try:
            # Save devices to storage
            await self._save_devices()

            # Notify Home Assistant that new devices are available
            # This will trigger the platform setup functions to create entities
            self.async_set_updated_data({
                "devices": self.devices,
                "states": self._device_states,
                "connected": self.connection.connected,
                "discovery_complete": True
            })
            _LOGGER.info("Triggered entity creation for %d devices", len(self.devices))

        except Exception as err:
            _LOGGER.error("Error triggering entity creation: %s", err)

    async def async_control_switch(self, device_id: str, channel: int, state: bool) -> bool:
        """Control switch device."""
        return await self.device_manager.control_switch(device_id, channel, state)

    async def async_control_light_brightness(self, device_id: str, brightness: int) -> bool:
        """Control light brightness."""
        return await self.device_manager.control_light_brightness(device_id, brightness)

    async def async_control_light_color_temp(self, device_id: str, color_temp: int) -> bool:
        """Control light color temperature."""
        return await self.device_manager.control_light_color_temp(device_id, color_temp)

    def get_device(self, device_id: str) -> SymiDevice | None:
        """Get device by ID."""
        return self.devices.get(device_id)

    def get_device_state(self, device_id: str) -> dict[str, Any]:
        """Get device state."""
        return self._device_states.get(device_id, {})

    def add_status_callback(self, device_id: str, callback: Callable) -> None:
        """Add status update callback for specific device."""
        if device_id not in self._status_callbacks:
            self._status_callbacks[device_id] = []
        self._status_callbacks[device_id].append(callback)

    def remove_status_callback(self, device_id: str, callback: Callable) -> None:
        """Remove status update callback."""
        if device_id in self._status_callbacks and callback in self._status_callbacks[device_id]:
            self._status_callbacks[device_id].remove(callback)

    def _handle_status_update(self, device_id: str, updated_states: dict[str, Any]) -> None:
        """Handle device status update."""
        # Update internal state
        if device_id not in self._device_states:
            self._device_states[device_id] = {}
        self._device_states[device_id].update(updated_states)
        
        # Call device-specific callbacks
        if device_id in self._status_callbacks:
            for callback in self._status_callbacks[device_id]:
                try:
                    callback(updated_states)
                except Exception as err:
                    _LOGGER.error("Error in status callback for %s: %s", device_id, err)
        
        # Trigger coordinator update to notify all entities
        self.async_set_updated_data(self.data or {})

    async def async_shutdown(self) -> None:
        """Shutdown coordinator."""
        _LOGGER.info("Shutting down Symi Gateway coordinator")
        
        # Remove status callback
        self.device_manager.remove_status_callback(self._handle_status_update)
        
        # Disconnect from gateway
        if self.connection.connected:
            await self.connection.disconnect()


async def async_get_coordinator(hass: HomeAssistant, entry: ConfigEntry) -> SymiGatewayCoordinator:
    """Get coordinator for the given config entry."""
    coordinator = SymiGatewayCoordinator(hass, entry)
    
    # Initial data fetch
    await coordinator.async_config_entry_first_refresh()
    
    return coordinator