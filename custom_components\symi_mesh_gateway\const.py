"""Constants for the Symi Mesh Gateway integration."""
from __future__ import annotations

DOMAIN = "symi_mesh_gateway"

# Configuration keys
CONF_HOST = "host"
CONF_PORT = "port"
CONF_TIMEOUT = "timeout"

# Default values
DEFAULT_PORT = 4196
DEFAULT_TIMEOUT = 10
DEFAULT_SCAN_INTERVAL = 30

# Protocol constants
PROTOCOL_HEAD = 0x53
DISCOVERY_COMMAND = [0x53, 0x12, 0x00, 0x41]  # 发现设备命令（4字节）

# Operation codes
OP_READ_DEVICE_LIST = 0x12
OP_DEVICE_LIST_RESPONSE = 0x92
OP_DEVICE_CONTROL = 0x30
OP_DEVICE_CONTROL_RESPONSE = 0xB0
OP_DEVICE_STATUS_EVENT = 0x80

# Device types mapping (based on support_dev_type_list_t from protocol doc)
DEVICE_TYPE_MAPPING = {
    0: "未知设备",
    1: "零火开关",
    2: "单火开关",
    3: "智能插座",
    4: "智能灯",
    5: "智能窗帘",
    6: "情景面板",
    7: "门磁传感器",
    8: "人体感应",
    9: "插卡取电",
    10: "温控器",
    11: "温湿度传感器",
    12: "情景开关",
    13: "离线语控节点",
    14: "智能门锁",
    15: "水浸报警传感器",
    16: "烟雾报警传感器",
    17: "智能电视盒子",
    18: "单火情景开关",
    19: "智能调光器",  # Additional type
    20: "透传模块",
    21: "智能面板",  # Additional type
    22: "智能遥控器",  # Additional type
    23: "智能网关",  # Additional type
    24: "五色调光灯",
    25: "RGB灯带",  # Additional type
    26: "智能风扇",  # Additional type
    27: "智能开关",  # Type 27 found in actual device data
    28: "智能调速器",  # Additional type
    # Extended types for special devices
    30: "智能音响",
    31: "智能摄像头",
    32: "智能门铃",
    # Transparent module types
    74: "透传模块",
    75: "透传开关",
    76: "透传传感器",
    # Mesh transparent types
    0x4A: "Mesh透传模块",  # Common transparent module type
    0x4B: "Mesh透传设备",
    0x4C: "Mesh透传开关",
    0x4D: "Mesh透传传感器",
}

# Platform mapping (complete mapping based on protocol doc)
DEVICE_TYPE_TO_PLATFORM = {
    0: None,          # 未知设备
    1: "switch",      # 零火开关
    2: "switch",      # 单火开关
    3: "switch",      # 智能插座
    4: "light",       # 智能灯
    5: "cover",       # 智能窗帘
    6: None,          # 情景面板 (不创建实体)
    7: "binary_sensor", # 门磁传感器
    8: "binary_sensor", # 人体感应
    9: "switch",      # 插卡取电
    10: "climate",    # 温控器
    11: "sensor",     # 温湿度传感器
    12: "switch",     # 情景开关
    13: None,         # 离线语控节点
    14: "lock",       # 智能门锁
    15: "binary_sensor", # 水浸报警传感器
    16: "binary_sensor", # 烟雾报警传感器
    17: None,         # 智能电视盒子
    18: "switch",     # 单火情景开关
    19: "light",      # 智能调光器
    20: "switch",     # 透传模块 (作为开关)
    21: "switch",     # 智能面板
    22: None,         # 智能遥控器 (不创建实体)
    23: None,         # 智能网关 (不创建实体)
    24: "light",      # 五色调光灯
    25: "light",      # RGB灯带
    26: "fan",        # 智能风扇
    27: "switch",     # 智能开关
    28: "light",      # 智能调速器 (作为调光器)
    # Extended types
    30: None,         # 智能音响 (暂不支持)
    31: None,         # 智能摄像头 (暂不支持)
    32: "binary_sensor", # 智能门铃
    # Transparent module types
    74: "switch",     # 透传模块 (作为开关)
    75: "switch",     # 透传开关
    76: "sensor",     # 透传传感器
    # Mesh transparent types
    0x4A: "switch",   # Mesh透传模块
    0x4B: "switch",   # Mesh透传设备
    0x4C: "switch",   # Mesh透传开关
    0x4D: "sensor",   # Mesh透传传感器
}

# Message types (based on vd_msg_type_t from protocol doc)
MSG_TYPE_ON_OFF = 0x02
MSG_TYPE_LIGHT_BRIGHTNESS = 0x03
MSG_TYPE_LIGHT_COLOR_TEMP = 0x04
MSG_TYPE_CURTAIN_STATUS = 0x05
MSG_TYPE_CURTAIN_POSITION = 0x06
MSG_TYPE_SCENE_PANEL_EVENT = 0x07
MSG_TYPE_CURTAIN_CONFIG_DIR = 0x08
MSG_TYPE_DOOR_SENSOR_STATUS = 0x09
MSG_TYPE_BATTERY_LEVEL_STATUS = 0x0A
MSG_TYPE_HB_DET_STATUS = 0x0B
MSG_TYPE_CTRL_SOURCE = 0x0D
MSG_TYPE_CARD_DET_STATUS = 0x0E
MSG_TYPE_SOFT_VERSION = 0x0F
MSG_TYPE_STATUS_CHG_RPT_EN_CFG = 0x10
MSG_TYPE_PAIR_FUNC_LOCK_CFG = 0x11
MSG_TYPE_KEYS_1_4_CFG = 0x12
MSG_TYPE_TEMP_SLEEP_MODE = 0x13
MSG_TYPE_SENSOR_TEMP = 0x16
MSG_TYPE_SENSOR_HUMI = 0x17
MSG_TYPE_KEYS_5_6_CFG = 0x19
MSG_TYPE_TMPC_TEMP = 0x1B
MSG_TYPE_TMPC_WIND_SPEED = 0x1C
MSG_TYPE_TMPC_MODE = 0x1D
MSG_TYPE_TMPC_KB_LOCK = 0x1E
MSG_TYPE_TMPC_VALVE = 0x1F
MSG_TYPE_TMPC_SLEEP = 0x21
MSG_TYPE_PWR_ON_AUTO_ONOFF_CFG = 0x24
MSG_TYPE_KEYS_ON_MUTEX_CFG = 0x25
MSG_TYPE_DEV_LOCK_CFG = 0x26
MSG_TYPE_KEY_LOOP_SCENE_CFG = 0x27
MSG_TYPE_SENSOR_WATER_ALARM = 0x2A
MSG_TYPE_SENSOR_SMOKE_ALARM = 0x2B
MSG_TYPE_LOW_BATTERY_LEVEL_ALARM = 0x2C
MSG_TYPE_TAMPER_ALARM = 0x2D
MSG_TYPE_LIGHT_LIGHTNESS_INC = 0x2F
MSG_TYPE_LIGHT_LIGHTNESS_DEC = 0x30
MSG_TYPE_LIGHT_TEMPERATURE_INC = 0x31
MSG_TYPE_LIGHT_TEMPERATURE_DEC = 0x32
MSG_TYPE_TMPC_TEMP_INC = 0x33
MSG_TYPE_TMPC_TEMP_DEC = 0x34
MSG_TYPE_TMPC_WIND_SPEED_INC = 0x35
MSG_TYPE_TMPC_WIND_SPEED_DEC = 0x36
MSG_TYPE_IR_ACTION = 0x8F

# Control values
CONTROL_OFF = 0x01
CONTROL_ON = 0x02

# Event types
RESULT_CMD_OK = 0
RESULT_EVENT_NODE_STATUS = 6

# Discovery settings
DISCOVERY_TIMEOUT = 5
DISCOVERY_PORT = 4196
DISCOVERY_BROADCAST_ADDR = "***************"

# Device data format (16 bytes)
DEVICE_DATA_SIZE = 16
DEVICE_MAC_OFFSET = 0      # 6 bytes
DEVICE_ADDR_OFFSET = 6     # 2 bytes (little endian)
DEVICE_TYPE_OFFSET = 8     # 1 byte
DEVICE_SUBTYPE_OFFSET = 9  # 1 byte (channels)
DEVICE_RSSI_OFFSET = 10    # 1 byte
DEVICE_VENDOR_OFFSET = 11  # 1 byte
DEVICE_EXT_OFFSET = 12     # 4 bytes

# Error messages
ERROR_CANNOT_CONNECT = "cannot_connect"
ERROR_INVALID_HOST = "invalid_host"
ERROR_TIMEOUT = "timeout"
ERROR_UNKNOWN = "unknown"

# Entity names
ENTITY_NAME_FORMAT = "{device_name} {channel_name}"
SWITCH_CHANNEL_NAMES = ["开关", "开关1", "开关2", "开关3", "开关4", "开关5", "开关6"]