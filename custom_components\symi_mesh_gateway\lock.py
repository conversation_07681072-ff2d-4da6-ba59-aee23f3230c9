"""Lock platform for Symi Mesh Gateway."""
from __future__ import annotations

import logging
from typing import Any

from homeassistant.components.lock import LockEntity
from homeassistant.config_entries import ConfigEntry
from homeassistant.core import HomeAssistant
from homeassistant.helpers.entity_platform import AddEntitiesCallback

from .const import DOMAIN
from .coordinator import SymiGatewayCoordinator
from .entity import SymiBaseEntity
from .protocol import SymiDevice

_LOGGER = logging.getLogger(__name__)


async def async_setup_entry(
    hass: HomeAssistant,
    entry: ConfigEntry,
    async_add_entities: AddEntitiesCallback,
) -> None:
    """Set up lock entities."""
    coordinator: SymiGatewayCoordinator = hass.data[DOMAIN][entry.entry_id]
    
    entities = []
    
    for device in coordinator.devices.values():
        # Check if device should create lock entities
        if device.platform == "lock":
            entities.append(SymiLockEntity(coordinator, device))
    
    if entities:
        async_add_entities(entities)
        _LOGGER.info("Added %d lock entities", len(entities))


class SymiLockEntity(SymiBaseEntity, LockEntity):
    """Symi lock entity."""

    def __init__(
        self,
        coordinator: SymiGatewayCoordinator,
        device: SymiDevice,
    ) -> None:
        """Initialize the lock."""
        super().__init__(coordinator, device)
        
        self._attr_icon = "mdi:lock"

    @property
    def is_locked(self) -> bool | None:
        """Return true if lock is locked."""
        device_state = self.coordinator.get_device_state(self.device_id)
        return device_state.get("locked", False)

    async def async_lock(self, **kwargs: Any) -> None:
        """Lock the lock."""
        # Send lock control command
        from .protocol import SymiProtocol
        command = SymiProtocol.create_control_command(
            self._device.network_address,
            0x02,  # ON_OFF message type
            0x02   # Lock (same as ON)
        )
        
        response_data = await self.coordinator.connection.send_command(command)
        if response_data:
            response = SymiProtocol.parse_response(response_data)
            if response and response["status"] == 0:  # RESULT_CMD_OK
                # Update state immediately
                self.coordinator._device_states.setdefault(self.device_id, {})["locked"] = True
                self.async_write_ha_state()
                _LOGGER.debug("Lock command sent successfully: %s", self.device_id)
            else:
                _LOGGER.error("Failed to lock %s", self.device_id)
        else:
            _LOGGER.error("No response for lock command: %s", self.device_id)

    async def async_unlock(self, **kwargs: Any) -> None:
        """Unlock the lock."""
        # Send unlock control command
        from .protocol import SymiProtocol
        command = SymiProtocol.create_control_command(
            self._device.network_address,
            0x02,  # ON_OFF message type
            0x01   # Unlock (same as OFF)
        )
        
        response_data = await self.coordinator.connection.send_command(command)
        if response_data:
            response = SymiProtocol.parse_response(response_data)
            if response and response["status"] == 0:  # RESULT_CMD_OK
                # Update state immediately
                self.coordinator._device_states.setdefault(self.device_id, {})["locked"] = False
                self.async_write_ha_state()
                _LOGGER.debug("Unlock command sent successfully: %s", self.device_id)
            else:
                _LOGGER.error("Failed to unlock %s", self.device_id)
        else:
            _LOGGER.error("No response for unlock command: %s", self.device_id)

    def _handle_status_update(self, updated_states: dict[str, Any]) -> None:
        """Handle status update from coordinator."""
        if "locked" in updated_states:
            self.async_write_ha_state()

    @property
    def extra_state_attributes(self) -> dict[str, Any]:
        """Return extra state attributes."""
        return {
            "device_type": self._device.device_type,
            "network_address": f"0x{self._device.network_address:04X}",
            "rssi": self._device.rssi,
            "vendor_id": self._device.vendor_id,
        }