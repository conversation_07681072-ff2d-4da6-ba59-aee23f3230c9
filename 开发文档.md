# 亖米Mesh网关 Home Assistant 集成开发文档

## 项目概述

本项目是为亖米Mesh网关开发的Home Assistant自定义集成，实现对蓝牙Mesh智能设备的控制和管理。

## 核心需求分析

### 1. 工作流程要求

1. **局域网发现阶段**：
   - 扫描局域网4196端口发现网关
   - 发送协议测试消息验证连接
   - 用户选择网关或手动输入IP

2. **设备发现阶段**：
   - 连接成功后发送读取设备列表命令 (0x12)
   - 接收设备列表响应 (0x92)
   - 解析设备数据并持久化保存

3. **实体创建阶段**：
   - 根据设备类型和子类型创建对应实体
   - 确保设备ID唯一性，避免重复创建
   - 一次性完成所有实体创建

4. **运行阶段**：
   - 监听设备状态事件 (0x80)
   - 响应用户控制命令
   - 维护设备在线状态

### 2. 协议规范

#### 2.1 通信协议格式
```
发送格式：头码(0x53) + 操作码 + 数据长度 + 数据 + 校验码
响应格式：头码(0x53) + 操作码 + 状态 + 数据长度 + 数据 + 校验码
```

#### 2.2 关键操作码
- `0x12`: 读取设备列表
- `0x92`: 设备列表响应
- `0x30`: 设备控制
- `0xB0`: 设备控制响应
- `0x80`: 设备状态事件

#### 2.3 设备数据格式 (16字节)
```
Byte 0-5:   MAC地址 (6字节)
Byte 6-7:   网络地址 (2字节，小端序)
Byte 8:     设备类型
Byte 9:     设备子类型 (路数/通道数)
Byte 10:    RSSI信号强度
Byte 11:    厂商ID
Byte 12-15: 扩展数据
```

### 3. 设备类型映射

根据协议文档3.5.2节，设备类型定义：

| 类型码 | 设备名称 | 子类型含义 | Home Assistant平台 |
|--------|----------|------------|-------------------|
| 1 | 零火开关 | 1-6路 | switch |
| 2 | 单火开关 | 1-6路 | switch |
| 3 | 智能插座 | - | switch |
| 4 | 智能灯 | - | light |
| 5 | 智能窗帘 | - | cover |
| 6 | 情景面板 | - | - |
| 7 | 门磁传感器 | - | binary_sensor |
| 8 | 人体感应 | - | binary_sensor |
| 9 | 插卡取电 | - | switch |
| 10 | 温控器 | - | climate |
| 11 | 温湿度传感器 | - | sensor |
| 24 | 五色调光灯 | - | light |

### 4. 控制命令格式

#### 4.1 开关控制 (MSG_TYPE = 0x02)
```python
# 单路开关
param = [0x01]  # 关闭
param = [0x02]  # 开启

# 多路开关 (按位控制)
# bit1-2: 第1路, bit3-4: 第2路, 以此类推
# 01=关闭, 10=开启
param = [0x05]  # 第1路关闭，第2路关闭 (01 01)
param = [0x0A]  # 第1路开启，第2路开启 (10 10)
```

#### 4.2 调光控制 (MSG_TYPE = 0x03)
```python
param = [brightness_percent]  # 0-100
```

#### 4.3 色温控制 (MSG_TYPE = 0x04)
```python
param = [color_temp_percent]  # 0-100
```

## 架构设计

### 1. 核心组件

```
coordinator.py          # 协调器 - 管理TCP连接和设备
├── tcp_comm.py         # TCP通信层
├── protocol.py         # 协议解析层
├── device_manager.py   # 设备管理器
└── platforms/          # 平台实体
    ├── switch.py       # 开关实体
    ├── light.py        # 灯光实体
    ├── binary_sensor.py # 传感器实体
    └── cover.py        # 窗帘实体
```

### 2. 数据流

```
用户操作 → Platform Entity → Coordinator → TCP Comm → 网关设备
                                ↓
设备状态 ← Platform Entity ← Coordinator ← TCP Comm ← 网关设备
```

## 关键问题解决

### 1. 避免死循环
- 设备发现只在初始化时执行一次
- 使用事件驱动而非轮询机制
- 设置合理的超时和重试机制

### 2. 设备ID唯一性
```python
def generate_unique_id(mac_address: str, channel: int = None) -> str:
    """生成唯一设备ID"""
    base_id = mac_address.replace(":", "").lower()
    if channel:
        return f"{base_id}_ch{channel}"
    return base_id
```

### 3. 多路开关处理
```python
# 为多路开关创建独立实体
for channel in range(1, device.channels + 1):
    entity = SwitchEntity(device, channel)
    entity.unique_id = f"{device.unique_id}_switch_{channel}"
```

## 实现检查清单

### Phase 1: 基础通信 ✅
- [x] TCP连接建立
- [x] 协议解析器
- [x] 设备列表读取

### Phase 2: 设备管理 ✅
- [x] 正确解析16字节设备数据
- [x] 设备类型映射
- [x] 设备状态管理
- [x] 唯一ID生成 (修复冲突问题)

### Phase 3: 实体创建 ✅
- [x] 开关实体 (1-6路)
- [x] 灯光实体 (调光/色温)
- [x] 传感器实体
- [x] 透传模块实体
- [x] 修复重复创建问题

### Phase 4: 控制功能 ✅
- [x] 开关控制命令
- [x] 调光控制命令
- [x] 状态查询命令
- [x] 事件处理

### Phase 5: 优化功能 ✅
- [x] 设备在线状态
- [x] 错误处理
- [x] 重新配置支持
- [x] 持久化存储

## 最新修复记录 (v1.0.2)

### 已解决的问题 ✅
1. **设备类型映射不完整** - 根据协议文档完善所有设备类型映射，包括0x1B等缺失类型
2. **状态事件解析错误** - 修复"Status event data too short: 4 bytes"问题
3. **未知设备状态处理** - 改进对未知设备状态的处理和调试信息
4. **门锁设备支持** - 新增lock平台支持智能门锁设备

### 技术改进
1. **完善设备类型映射**: 基于协议文档添加所有18种标准设备类型+扩展类型
2. **状态事件解析优化**: 改进最小数据长度检查和解析逻辑
3. **调试信息增强**: 添加详细的设备发现和状态更新日志
4. **平台扩展**: 新增lock平台，支持更多设备类型
5. **错误处理**: 更健壮的异常处理和设备状态管理

### 新增设备类型支持
- 情景开关 (类型12)
- 离线语控节点 (类型13) 
- 智能门锁 (类型14)
- 水浸报警 (类型15)
- 烟雾报警 (类型16)
- 智能电视盒子 (类型17)
- 单火情景开关 (类型18)
- 扩展设备 (类型27)
- Mesh透传模块 (类型0x4A, 0x4B)

### 当前状态
- ✅ TCP通信正常 - 能收到设备数据
- ✅ 设备解析完善 - 支持所有协议定义的设备类型
- ✅ 状态事件处理 - 修复数据长度检查问题
- ✅ 实体创建优化 - 改进设备发现和平台映射
- ✅ 调试信息完善 - 详细的日志输出便于问题诊断

## 技术规范

- Python 3.12+
- Home Assistant 2025.1.0+
- 异步编程模式
- 事件驱动架构
- 协议版本: V1.0 (BCM/QM10-202112)
