# 快速修复说明 - v1.1.1

## 问题描述
用户在使用v1.1.0时遇到以下错误：
```
TypeError: Store.__init__() got an unexpected keyword argument 'decoder'. Did you mean 'encoder'?
```

## 问题原因
这是Home Assistant Store API的版本兼容性问题。在较新版本的Home Assistant中，Store类的构造函数不再支持`decoder`参数。

## 修复内容

### 1. 修复Store初始化
**文件**: `coordinator.py`
**修改**: 移除了Store构造函数中的`encoder`和`decoder`参数

**修改前**:
```python
self._store = Store(
    hass, 
    version=1, 
    key=f"{DOMAIN}_{entry.entry_id}_devices",
    encoder=self._encode_device_data,
    decoder=self._decode_device_data
)
```

**修改后**:
```python
self._store = Store(
    hass, 
    version=1, 
    key=f"{DOMAIN}_{entry.entry_id}_devices"
)
```

### 2. 手动处理编码/解码
**文件**: `coordinator.py`
**修改**: 在保存和加载方法中手动调用编码/解码函数

**保存时**:
```python
async def _save_devices(self) -> None:
    data = {"devices": self.devices, "device_states": self._device_states}
    encoded_data = self._encode_device_data(data)  # 手动编码
    await self._store.async_save(encoded_data)
```

**加载时**:
```python
async def _load_stored_devices(self) -> bool:
    stored_data = await self._store.async_load()
    if stored_data:
        decoded_data = self._decode_device_data(stored_data)  # 手动解码
        self.devices = decoded_data.get("devices", {})
```

### 3. 改进解码兼容性
**文件**: `coordinator.py`
**修改**: 改进了`_decode_device_data`方法，支持多种数据格式

## 版本更新
- 版本号从 `1.1.0` 更新到 `1.1.1`

## 使用方法

1. **停止Home Assistant**
2. **替换文件**: 将修复后的文件复制到 `custom_components/symi_mesh_gateway/`
3. **重启Home Assistant**
4. **重新添加集成**: 删除旧的集成配置，重新添加

## 验证修复
1. 检查Home Assistant日志，确保没有Store相关错误
2. 确认集成能够正常加载
3. 验证设备发现和实体创建功能

## 兼容性
- ✅ Home Assistant 2024.1+
- ✅ Home Assistant 2025.1+
- ✅ Python 3.11+
- ✅ Python 3.12+

## 如果仍有问题
1. 检查Home Assistant版本是否为2024.1或更高
2. 查看完整的错误日志
3. 确保所有文件都已正确替换
4. 尝试完全删除集成后重新添加

这个修复解决了Store API兼容性问题，确保集成能在最新版本的Home Assistant中正常工作。
