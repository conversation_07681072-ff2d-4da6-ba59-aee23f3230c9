# 亖米Mesh网关 Home Assistant 集成

这是亖米Mesh网关的Home Assistant自定义集成，支持蓝牙Mesh智能设备的控制和管理。

## 项目编号

项目编号：303316404
仓库名称：symi-daguo

## 功能特性

### 🔧 核心功能
- ✅ **自动发现网关** - 扫描局域网4196端口自动发现网关
- ✅ **手动配置** - 支持手动输入网关IP地址
- ✅ **设备自动发现** - 连接网关后自动获取所有设备信息
- ✅ **实时状态同步** - 设备状态变化实时反馈到Home Assistant
- ✅ **持久化存储** - 设备信息和状态持久化保存

### 📱 支持的设备类型

| 设备类型 | 设备名称 | Home Assistant平台 | 功能支持 |
|---------|----------|-------------------|----------|
| 1 | 零火开关 | switch | 1-6路开关控制 |
| 2 | 单火开关 | switch | 1-6路开关控制 |
| 3 | 智能插座 | switch | 开关控制 |
| 4 | 智能灯 | light | 开关/亮度/色温控制 |
| 5 | 智能窗帘 | cover | 开关/位置控制 |
| 7 | 门磁传感器 | binary_sensor | 开关状态检测 |
| 8 | 人体感应 | binary_sensor | 运动检测 |
| 9 | 插卡取电 | switch | 开关控制 |
| 10 | 温控器 | climate | 温度/模式/风速控制 |
| 11 | 温湿度传感器 | sensor | 温度/湿度监测 |
| 24 | 五色调光灯 | light | 开关/亮度/色温控制 |
| 20/74 | 透传模块 | switch | 开关控制 |

## 安装方法

### 方法1：手动安装

1. 下载本集成的所有文件
2. 在Home Assistant配置目录创建 `custom_components/symi_mesh_gateway/` 文件夹
3. 将所有文件复制到该文件夹中
4. 重启Home Assistant
5. 在集成页面添加"Symi Mesh Gateway"集成

### 方法2：HACS安装（推荐）

1. 确保已安装HACS
2. 在HACS中添加自定义仓库：`https://github.com/symi-daguo/symi_mesh_gateway`
3. 搜索并安装"Symi Mesh Gateway"
4. 重启Home Assistant
5. 在集成页面添加集成

## 配置说明

### 自动发现配置

1. 进入Home Assistant的"设置" → "设备与服务"
2. 点击"添加集成"，搜索"Symi Mesh Gateway"
3. 集成会自动扫描局域网中的网关
4. 选择要配置的网关或选择"手动配置"

### 手动配置

如果自动发现失败，可以手动配置：

- **网关IP地址**：网关的局域网IP地址
- **端口**：通信端口（默认4196）
- **超时时间**：连接超时时间（默认10秒）

## 使用说明

### 设备控制

配置完成后，集成会自动发现所有连接到网关的设备，并创建对应的实体：

#### 开关设备
- 单路开关：创建1个开关实体
- 多路开关：为每路创建独立的开关实体

#### 灯光设备
- 支持开关控制
- 支持亮度调节（0-100%）
- 支持色温调节（智能灯和五色调光灯）

#### 传感器设备
- 门磁传感器：显示开/关状态
- 人体感应：显示有人/无人状态
- 温湿度传感器：显示温度和湿度值

#### 窗帘设备
- 支持开启/关闭/停止操作
- 支持位置控制（0-100%）

#### 温控器设备
- 支持温度设定
- 支持模式切换（制冷/制热/送风/关闭）
- 支持风速调节

### 实时状态更新

所有设备状态变化都会实时同步到Home Assistant：
- 本地操作设备会立即反馈状态
- 支持多个控制端同时使用
- 设备在线状态监测

## 协议说明

### 通信协议

集成使用TCP协议与网关通信，端口默认为4196：

```
发送格式：头码(0x53) + 操作码 + 数据长度 + 数据 + 校验码
响应格式：头码(0x53) + 操作码 + 状态 + 数据长度 + 数据 + 校验码
```

### 设备发现

发送指令 `53 12 00 41` 可获取所有设备的ID地址、类型和状态信息。

### 设备控制

- **开关控制**：MSG_TYPE = 0x02
- **亮度控制**：MSG_TYPE = 0x03  
- **色温控制**：MSG_TYPE = 0x04

## 故障排除

### 常见问题

1. **无法发现网关**
   - 确认网关和Home Assistant在同一局域网
   - 检查网关是否正常工作
   - 尝试手动配置网关IP

2. **设备无法控制**
   - 检查网关连接状态
   - 确认设备在网关的设备列表中
   - 查看Home Assistant日志获取详细错误信息

3. **状态不同步**
   - 检查网络连接稳定性
   - 重启网关和Home Assistant
   - 查看实体状态是否为"不可用"

### 调试日志

启用DEBUG级别日志查看详细信息：

```yaml
logger:
  default: info
  logs:
    custom_components.symi_mesh_gateway: debug
```

## 测试验证

运行测试脚本验证集成功能：

```bash
python test_symi_integration.py
```

测试脚本会：
- 自动发现网关
- 测试连接和通信
- 发现设备并测试控制功能
- 验证协议解析

## 技术规范

- **Python版本**：3.12+
- **Home Assistant版本**：2025.1.0+
- **通信协议**：TCP
- **设备协议**：Symi Mesh V1.0
- **架构模式**：异步编程，事件驱动

## 贡献

欢迎提交Issue和Pull Request来改进这个集成。

## 许可证

本项目使用MIT许可证。

## 联系方式

- 项目仓库：https://github.com/symi-daguo/symi_mesh_gateway
- 项目编号：303316404